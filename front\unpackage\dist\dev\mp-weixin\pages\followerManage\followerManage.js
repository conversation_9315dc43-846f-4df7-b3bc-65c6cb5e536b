"use strict";
const common_vendor = require("../../common/vendor.js");
const utils_followerUserManager = require("../../utils/followerUserManager.js");
const utils_warehouseUsers = require("../../utils/warehouseUsers.js");
const utils_urlConfig = require("../../utils/urlConfig.js");
const utils_helpers = require("../../utils/helpers.js");
const utils_apiService = require("../../utils/apiService.js");
const _sfc_main = {
  data() {
    return {
      currentUser: null,
      searchKeyword: "",
      searchResults: [],
      orderHistory: [],
      isLoading: false,
      showNoResults: false,
      searchTimeout: null,
      // 高级检索相关
      showAdvancedSearch: false,
      advancedFilters: {
        factoryName: "",
        startDate: "",
        endDate: ""
      },
      // 短信通知设置相关
      notificationSettings: {
        allowed: false,
        phone: "",
        hasPhone: false
      },
      phoneInput: "",
      isUpdatingPhone: false
    };
  },
  methods: {
    /**
     * 处理搜索输入
     */
    onSearchInput() {
      this.searchResults = [];
      this.showNoResults = false;
      if (this.searchTimeout) {
        clearTimeout(this.searchTimeout);
      }
      if (this.searchKeyword.trim()) {
        this.searchTimeout = setTimeout(() => {
          this.handleSearch();
        }, 500);
      }
    },
    /**
     * 处理搜索
     */
    async handleSearch() {
      if (!this.searchKeyword.trim()) {
        this.searchResults = [];
        this.showNoResults = false;
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:357", "🔍 搜索订单:", this.searchKeyword);
        const token = utils_followerUserManager.followerUserManager.getToken();
        if (!token) {
          utils_helpers.showError("请先登录");
          return;
        }
        const response = await utils_apiService.apiService.request({
          url: `/api/orders/follower-query/${encodeURIComponent(this.searchKeyword.trim())}`,
          method: "GET",
          header: {
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.success) {
          if (Array.isArray(response.data)) {
            this.searchResults = response.data;
          } else {
            this.searchResults = [response.data];
          }
          this.showNoResults = false;
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:381", "✅ 搜索成功，找到", this.searchResults.length, "个订单");
        } else {
          this.searchResults = [];
          this.showNoResults = true;
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:385", "⚠️ 搜索无结果:", response.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:388", "❌ 搜索订单失败:", error);
        this.searchResults = [];
        this.showNoResults = true;
        utils_helpers.showError("搜索失败，请稍后重试");
      }
    },
    /**
     * 查看订单详情
     */
    async viewOrderDetail(order) {
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:399", "📋 查看订单详情:", order.order_number);
      try {
        common_vendor.index.showLoading({
          title: "获取访问权限..."
        });
        const currentFollowerInfo = utils_followerUserManager.followerUserManager.getUserInfo();
        common_vendor.index.setStorageSync("follower_user_info", currentFollowerInfo);
        const accessToken = await this.getFactoryAccessToken(order.factory_name);
        if (accessToken) {
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:415", "✅ 获取到工厂访问token:", accessToken);
          common_vendor.index.setStorageSync("temp_factory_access_token", accessToken);
          try {
            utils_warehouseUsers.warehouseUserManager.logout();
            common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:422", "✅ 跟单员访问开始，已清除仓库管理登录状态");
          } catch (error) {
            common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:424", "❌ 清除仓库管理登录状态失败:", error);
          }
          common_vendor.index.navigateTo({
            url: `/pages/imageManage/imageManage?orderNumber=${order.order_number}&factoryName=${order.factory_name}&readonly=true&followerAccess=true`
          });
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:433", "❌ 获取工厂访问权限失败:", error);
        utils_helpers.showError(error.message || "获取访问权限失败");
      } finally {
        common_vendor.index.hideLoading();
      }
    },
    /**
     * 获取工厂访问token
     */
    async getFactoryAccessToken(factoryName) {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_followerUserManager.followerUserManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl("/api/auth/follower-factory-access"),
          method: "POST",
          data: {
            factory_name: factoryName
          },
          timeout: 1e4,
          success: (res) => {
            var _a;
            if (res.statusCode === 200 && res.data.success) {
              resolve(res.data.data);
            } else {
              reject(new Error(((_a = res.data) == null ? void 0 : _a.message) || "获取访问权限失败"));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:460", "获取工厂访问token请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 格式化日期
     */
    formatDate(dateString) {
      if (!dateString)
        return "未知时间";
      try {
        const date = new Date(dateString);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, "0");
        const day = String(date.getDate()).padStart(2, "0");
        const hours = String(date.getHours()).padStart(2, "0");
        const minutes = String(date.getMinutes()).padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}`;
      } catch (error) {
        return "时间格式错误";
      }
    },
    /**
     * 加载订单历史
     */
    async loadOrderHistory() {
      try {
        this.isLoading = true;
        common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:495", "📋 加载跟单员订单历史...");
        const token = utils_followerUserManager.followerUserManager.getToken();
        if (!token) {
          utils_helpers.showError("请先登录");
          return;
        }
        const response = await utils_apiService.apiService.request({
          url: "/api/orders/follower-history",
          method: "GET",
          header: {
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.success) {
          this.orderHistory = response.data || [];
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:513", "✅ 订单历史加载成功，共", this.orderHistory.length, "个订单");
        } else {
          common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:515", "❌ 加载订单历史失败:", response.message);
          utils_helpers.showError("加载订单历史失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:519", "❌ 加载订单历史异常:", error);
        utils_helpers.showError("加载订单历史失败，请稍后重试");
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * 处理登出
     */
    handleLogout() {
      common_vendor.index.showModal({
        title: "确认登出",
        content: "您确定要退出登录吗？",
        success: (res) => {
          if (res.confirm) {
            const logoutSuccess = utils_followerUserManager.followerUserManager.logout();
            if (logoutSuccess) {
              utils_helpers.showSuccess("已退出登录");
              setTimeout(() => {
                common_vendor.index.reLaunch({
                  url: "/pages/home/<USER>"
                });
              }, 1500);
            } else {
              utils_helpers.showError("退出登录失败");
            }
          }
        }
      });
    },
    /**
     * 初始化用户信息
     */
    initUserInfo() {
      this.currentUser = utils_followerUserManager.followerUserManager.getUserInfo();
      if (!this.currentUser) {
        common_vendor.index.reLaunch({
          url: "/pages/followerLogin/followerLogin"
        });
        return;
      }
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:570", "✅ 跟单员信息加载成功:", this.currentUser);
      this.loadOrderHistory();
      this.loadNotificationSettings();
    },
    /**
     * 加载短信通知设置
     */
    async loadNotificationSettings() {
      try {
        common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:584", "📱 加载短信通知设置...");
        const token = utils_followerUserManager.followerUserManager.getToken();
        if (!token) {
          common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:588", "❌ 未找到token");
          return;
        }
        const response = await utils_apiService.apiService.request({
          url: "/api/sms/notification-settings",
          method: "GET",
          header: {
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.success) {
          this.notificationSettings = response.data;
          this.phoneInput = response.data.phone || "";
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:603", "✅ 短信通知设置加载成功:", response.data);
        } else {
          common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:605", "❌ 加载短信通知设置失败:", response.message);
          utils_helpers.showError("加载短信通知设置失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:609", "❌ 加载短信通知设置异常:", error);
        utils_helpers.showError("加载短信通知设置失败，请稍后重试");
      }
    },
    /**
     * 短信通知开关切换
     */
    async onNotificationToggle(event) {
      const newValue = event.detail.value;
      if (!this.notificationSettings.hasPhone) {
        utils_helpers.showError("请先添加手机号码");
        return;
      }
      try {
        common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:626", "📱 切换短信通知开关:", newValue);
        const token = utils_followerUserManager.followerUserManager.getToken();
        if (!token) {
          utils_helpers.showError("请先登录");
          return;
        }
        const response = await utils_apiService.apiService.request({
          url: "/api/sms/notification-settings",
          method: "PUT",
          data: {
            allowed: newValue
          },
          header: {
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.success) {
          this.notificationSettings.allowed = newValue;
          utils_helpers.showSuccess(newValue ? "短信通知已开启" : "短信通知已关闭");
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:648", "✅ 短信通知开关更新成功");
        } else {
          common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:650", "❌ 更新短信通知开关失败:", response.message);
          utils_helpers.showError("更新失败：" + response.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:654", "❌ 更新短信通知开关异常:", error);
        utils_helpers.showError("更新失败，请稍后重试");
      }
    },
    /**
     * 更新手机号码
     */
    async updatePhone() {
      const phone = this.phoneInput.trim();
      if (!phone) {
        utils_helpers.showError("请输入手机号码");
        return;
      }
      const phoneRegex = /^1[3-9]\d{9}$/;
      if (!phoneRegex.test(phone)) {
        utils_helpers.showError("手机号格式不正确");
        return;
      }
      try {
        this.isUpdatingPhone = true;
        common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:679", "📱 更新手机号码:", phone);
        const token = utils_followerUserManager.followerUserManager.getToken();
        if (!token) {
          utils_helpers.showError("请先登录");
          return;
        }
        const response = await utils_apiService.apiService.request({
          url: "/api/sms/notification-settings",
          method: "PUT",
          data: {
            phone
          },
          header: {
            "Authorization": `Bearer ${token}`
          }
        });
        if (response.success) {
          this.notificationSettings.phone = phone;
          this.notificationSettings.hasPhone = true;
          utils_helpers.showSuccess("手机号码更新成功");
          common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:702", "✅ 手机号码更新成功");
        } else {
          common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:704", "❌ 更新手机号码失败:", response.message);
          utils_helpers.showError("更新失败：" + response.message);
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:708", "❌ 更新手机号码异常:", error);
        utils_helpers.showError("更新失败，请稍后重试");
      } finally {
        this.isUpdatingPhone = false;
      }
    },
    /**
     * 切换高级检索显示状态
     */
    toggleAdvancedSearch() {
      this.showAdvancedSearch = !this.showAdvancedSearch;
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:720", "🔽 切换高级检索:", this.showAdvancedSearch);
    },
    /**
     * 高级筛选条件变化
     */
    onAdvancedFilterChange() {
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:728", "🔍 高级筛选条件变化:", this.advancedFilters);
    },
    /**
     * 清除坯布商筛选
     */
    clearFactoryFilter() {
      this.advancedFilters.factoryName = "";
      this.onAdvancedFilterChange();
    },
    /**
     * 开始日期变化
     */
    onStartDateChange(e) {
      this.advancedFilters.startDate = e.detail.value;
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:744", "📅 开始日期变化:", this.advancedFilters.startDate);
      this.onAdvancedFilterChange();
    },
    /**
     * 结束日期变化
     */
    onEndDateChange(e) {
      this.advancedFilters.endDate = e.detail.value;
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:753", "📅 结束日期变化:", this.advancedFilters.endDate);
      this.onAdvancedFilterChange();
    },
    /**
     * 重置高级筛选条件
     */
    resetAdvancedFilters() {
      this.advancedFilters = {
        factoryName: "",
        startDate: "",
        endDate: ""
      };
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:766", "🔄 重置高级筛选条件");
      this.handleAdvancedSearch();
    },
    /**
     * 清空检索结果
     */
    clearSearchResults() {
      this.searchResults = [];
      this.showNoResults = false;
      this.searchKeyword = "";
      this.advancedFilters = {
        factoryName: "",
        startDate: "",
        endDate: ""
      };
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:783", "🗑️ 清空检索结果");
      utils_helpers.showSuccess("已清空检索结果");
    },
    /**
     * 执行高级检索
     */
    async handleAdvancedSearch() {
      common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:791", "🔍 执行高级检索:", this.advancedFilters);
      try {
        this.isLoading = true;
        this.searchResults = [];
        this.showNoResults = false;
        const queryParams = {};
        if (this.searchKeyword.trim()) {
          queryParams.orderNumber = this.searchKeyword.trim();
        }
        if (this.advancedFilters.factoryName.trim()) {
          queryParams.factoryName = this.advancedFilters.factoryName.trim();
        }
        if (this.advancedFilters.startDate) {
          queryParams.startDate = this.advancedFilters.startDate;
        }
        if (this.advancedFilters.endDate) {
          queryParams.endDate = this.advancedFilters.endDate;
        }
        common_vendor.index.__f__("log", "at pages/followerManage/followerManage.vue:819", "📋 高级检索参数:", queryParams);
        const response = await this.advancedSearchRequest(queryParams);
        if (response.success) {
          this.searchResults = response.data || [];
          this.showNoResults = this.searchResults.length === 0;
          if (this.searchResults.length > 0) {
            utils_helpers.showSuccess(`找到 ${this.searchResults.length} 条相关订单`);
          } else {
            utils_helpers.showError("未找到符合条件的订单");
          }
        } else {
          throw new Error(response.message || "检索失败");
        }
      } catch (error) {
        common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:837", "❌ 高级检索失败:", error);
        this.searchResults = [];
        this.showNoResults = true;
        utils_helpers.showError("检索失败，请稍后重试");
      } finally {
        this.isLoading = false;
      }
    },
    /**
     * 高级检索请求
     */
    async advancedSearchRequest(queryParams) {
      return new Promise((resolve, reject) => {
        const requestConfig = utils_followerUserManager.followerUserManager.createAuthRequest({
          url: utils_urlConfig.urlConfig.getApiUrl("/api/orders/follower-advanced-search"),
          method: "POST",
          data: queryParams,
          timeout: 1e4,
          success: (res) => {
            if (res.statusCode === 200) {
              resolve(res.data);
            } else {
              reject(new Error(`服务器错误 (${res.statusCode})`));
            }
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at pages/followerManage/followerManage.vue:864", "高级检索请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
        common_vendor.index.request(requestConfig);
      });
    },
    /**
     * 获取发货状态样式类名
     */
    getShippingStatusClass(status) {
      switch (status) {
        case "shipped":
        case "已发货":
          return "status-shipped";
        case "overdue":
        case "超时":
          return "status-overdue";
        case "pending":
        case "未发货":
          return "status-pending";
        case "通知发货":
          return "status-notified";
        default:
          return "status-unknown";
      }
    },
    /**
     * 根据position获取用户身份文本
     */
    getUserPositionText(position) {
      switch (position) {
        case 1:
          return "跟单员";
        case 2:
        case 3:
          return "订坯人员";
        default:
          return "跟单员";
      }
    }
  },
  onLoad() {
    if (!utils_followerUserManager.followerUserManager.checkLoginStatus()) {
      common_vendor.index.reLaunch({
        url: "/pages/followerLogin/followerLogin"
      });
      return;
    }
    this.initUserInfo();
  },
  onShow() {
    if (utils_followerUserManager.followerUserManager.checkLoginStatus()) {
      this.initUserInfo();
    }
  }
};
function _sfc_render(_ctx, _cache, $props, $setup, $data, $options) {
  return common_vendor.e({
    a: $data.currentUser
  }, $data.currentUser ? {
    b: common_vendor.t($data.currentUser.follower_name),
    c: common_vendor.t($options.getUserPositionText($data.currentUser.position)),
    d: common_vendor.o((...args) => $options.handleLogout && $options.handleLogout(...args))
  } : {}, {
    e: $data.notificationSettings.allowed,
    f: common_vendor.o((...args) => $options.onNotificationToggle && $options.onNotificationToggle(...args)),
    g: !$data.notificationSettings.hasPhone,
    h: !$data.notificationSettings.hasPhone
  }, !$data.notificationSettings.hasPhone ? {} : {}, {
    i: $data.isUpdatingPhone,
    j: $data.phoneInput,
    k: common_vendor.o(($event) => $data.phoneInput = $event.detail.value),
    l: common_vendor.t($data.notificationSettings.hasPhone ? "修改" : "添加"),
    m: common_vendor.o((...args) => $options.updatePhone && $options.updatePhone(...args)),
    n: $data.isUpdatingPhone,
    o: !$data.notificationSettings.hasPhone
  }, !$data.notificationSettings.hasPhone ? {} : {
    p: common_vendor.t($data.notificationSettings.allowed ? "✅" : "❌"),
    q: common_vendor.t($data.notificationSettings.allowed ? "开启" : "关闭")
  }, {
    r: common_vendor.o([($event) => $data.searchKeyword = $event.detail.value, (...args) => $options.onSearchInput && $options.onSearchInput(...args)]),
    s: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    t: $data.searchKeyword,
    v: common_vendor.o((...args) => $options.handleSearch && $options.handleSearch(...args)),
    w: common_vendor.t($data.showAdvancedSearch ? "🔼" : "🔽"),
    x: common_vendor.o((...args) => $options.toggleAdvancedSearch && $options.toggleAdvancedSearch(...args)),
    y: $data.showAdvancedSearch
  }, $data.showAdvancedSearch ? common_vendor.e({
    z: common_vendor.o([($event) => $data.advancedFilters.factoryName = $event.detail.value, (...args) => $options.onAdvancedFilterChange && $options.onAdvancedFilterChange(...args)]),
    A: $data.advancedFilters.factoryName,
    B: $data.advancedFilters.factoryName
  }, $data.advancedFilters.factoryName ? {
    C: common_vendor.o((...args) => $options.clearFactoryFilter && $options.clearFactoryFilter(...args))
  } : {}, {
    D: common_vendor.t($data.advancedFilters.startDate || "开始日期"),
    E: $data.advancedFilters.startDate,
    F: common_vendor.o((...args) => $options.onStartDateChange && $options.onStartDateChange(...args)),
    G: common_vendor.t($data.advancedFilters.endDate || "结束日期"),
    H: $data.advancedFilters.endDate,
    I: common_vendor.o((...args) => $options.onEndDateChange && $options.onEndDateChange(...args)),
    J: common_vendor.o((...args) => $options.resetAdvancedFilters && $options.resetAdvancedFilters(...args)),
    K: common_vendor.o((...args) => $options.clearSearchResults && $options.clearSearchResults(...args)),
    L: common_vendor.o((...args) => $options.handleAdvancedSearch && $options.handleAdvancedSearch(...args))
  }) : {}, {
    M: $data.searchResults.length > 0
  }, $data.searchResults.length > 0 ? {
    N: common_vendor.t($data.searchResults.length),
    O: common_vendor.f($data.searchResults, (order, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(order.order_number),
        b: common_vendor.t(order.image_count),
        c: common_vendor.t(order.order_status || order.shipping_status_text || "未知"),
        d: order.order_status === "超时" && order.delay_days
      }, order.order_status === "超时" && order.delay_days ? {
        e: common_vendor.t(order.delay_days)
      } : {}, {
        f: common_vendor.n($options.getShippingStatusClass(order.order_status || order.shipping_status)),
        g: common_vendor.t(order.supplier || order.factory_name || "未指定"),
        h: common_vendor.t(order.product_name || "未指定"),
        i: common_vendor.t($options.formatDate(order.created_at)),
        j: common_vendor.t(order.deliver_company || "未指定"),
        k: index,
        l: common_vendor.o(($event) => $options.viewOrderDetail(order), index)
      });
    })
  } : {}, {
    P: $data.showNoResults
  }, $data.showNoResults ? {} : {}, {
    Q: common_vendor.t($data.orderHistory.length),
    R: $data.orderHistory.length > 0
  }, $data.orderHistory.length > 0 ? {
    S: common_vendor.f($data.orderHistory, (order, index, i0) => {
      return common_vendor.e({
        a: common_vendor.t(order.order_number),
        b: common_vendor.t(order.image_count),
        c: common_vendor.t(order.order_status || order.shipping_status_text || "未知"),
        d: order.order_status === "超时" && order.delay_days
      }, order.order_status === "超时" && order.delay_days ? {
        e: common_vendor.t(order.delay_days)
      } : {}, {
        f: common_vendor.n($options.getShippingStatusClass(order.order_status || order.shipping_status)),
        g: common_vendor.t(order.supplier || order.factory_name || "未指定"),
        h: common_vendor.t(order.product_name || "未指定"),
        i: common_vendor.t($options.formatDate(order.created_at)),
        j: common_vendor.t(order.deliver_company || "未指定"),
        k: index,
        l: common_vendor.o(($event) => $options.viewOrderDetail(order), index)
      });
    })
  } : {}, {
    T: $data.orderHistory.length === 0 && !$data.isLoading
  }, $data.orderHistory.length === 0 && !$data.isLoading ? {} : {}, {
    U: $data.isLoading
  }, $data.isLoading ? {} : {});
}
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["render", _sfc_render], ["__scopeId", "data-v-edf95388"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/followerManage/followerManage.js.map


.container.data-v-edf95388 {
		min-height: 100vh;
		background: #f5f7fa;
		display: flex;
		flex-direction: column;
}

	/* 头部样式 */
.header.data-v-edf95388 {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		padding: 40rpx 30rpx 30rpx;
		color: white;
		display: flex;
		justify-content: space-between;
		align-items: center;
		box-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);
}
.title-section.data-v-edf95388 {
		flex: 1;
}
.title.data-v-edf95388 {
		font-size: 44rpx;
		font-weight: bold;
		margin-bottom: 8rpx;
		display: block;
}
.subtitle.data-v-edf95388 {
		font-size: 26rpx;
		opacity: 0.9;
		display: block;
}
.user-section.data-v-edf95388 {
		display: flex;
		align-items: center;
		gap: 20rpx;
}
.user-info.data-v-edf95388 {
		text-align: right;
}
.user-name.data-v-edf95388 {
		font-size: 32rpx;
		font-weight: bold;
		display: block;
		margin-bottom: 4rpx;
}
.user-dept.data-v-edf95388 {
		font-size: 24rpx;
		opacity: 0.8;
		display: block;
}
.logout-btn.data-v-edf95388 {
		width: 70rpx;
		height: 70rpx;
		background: rgba(255, 255, 255, 0.2);
		border: none;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		-webkit-backdrop-filter: blur(10rpx);
		        backdrop-filter: blur(10rpx);
		transition: all 0.3s ease;
}
.logout-btn.data-v-edf95388:active {
		background: rgba(255, 255, 255, 0.3);
		transform: scale(0.95);
}
.logout-icon.data-v-edf95388 {
		font-size: 32rpx;
		color: white;
}

	/* 主要内容区域 */
.main-content.data-v-edf95388 {
		flex: 1;
		padding: 0;
}

	/* 短信通知设置区域 */
.notification-section.data-v-edf95388 {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
}
.notification-card.data-v-edf95388 {
		margin-top: 20rpx;
}
.card-header.data-v-edf95388 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f0f0f0;
}
.card-title.data-v-edf95388 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.card-content.data-v-edf95388 {
		padding-top: 20rpx;
}
.phone-section.data-v-edf95388 {
		margin-bottom: 20rpx;
}
.phone-label.data-v-edf95388 {
		display: flex;
		align-items: center;
		margin-bottom: 15rpx;
}
.label-text.data-v-edf95388 {
		font-size: 28rpx;
		color: #666;
}
.required-mark.data-v-edf95388 {
		color: #ff4757;
		margin-left: 5rpx;
		font-size: 28rpx;
}
.phone-input-group.data-v-edf95388 {
		display: flex;
		gap: 20rpx;
		align-items: center;
}
.phone-input.data-v-edf95388 {
		flex: 1;
		height: 80rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e0e0e0;
		border-radius: 10rpx;
		font-size: 28rpx;
		background: #fff;
}
.phone-input.data-v-edf95388:focus {
		border-color: #667eea;
}
.phone-btn.data-v-edf95388 {
		height: 80rpx;
		padding: 0 30rpx;
		background: #667eea;
		color: white;
		border: none;
		border-radius: 10rpx;
		font-size: 28rpx;
		font-weight: bold;
}
.phone-btn.data-v-edf95388:disabled {
		background: #ccc;
}
.notification-tip.data-v-edf95388 {
		display: flex;
		align-items: center;
		gap: 10rpx;
		padding: 15rpx 20rpx;
		background: #fff3cd;
		border: 2rpx solid #ffeaa7;
		border-radius: 10rpx;
		margin-top: 20rpx;
}
.tip-icon.data-v-edf95388 {
		font-size: 28rpx;
}
.tip-text.data-v-edf95388 {
		font-size: 26rpx;
		color: #856404;
}
.notification-status.data-v-edf95388 {
		display: flex;
		align-items: center;
		gap: 10rpx;
		padding: 15rpx 20rpx;
		background: #f8f9fa;
		border-radius: 10rpx;
		margin-top: 20rpx;
}
.status-icon.data-v-edf95388 {
		font-size: 28rpx;
}
.status-text.data-v-edf95388 {
		font-size: 26rpx;
		color: #666;
}

	/* 搜索区域样式 */
.search-section.data-v-edf95388 {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin: 30rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
}

	/* 历史订单区域样式 */
.history-section.data-v-edf95388 {
		margin: 0 30rpx;
}
.section-title.data-v-edf95388 {
		display: flex;
		align-items: center;
		gap: 15rpx;
		margin-bottom: 25rpx;
		padding: 0 10rpx;
}
.title-icon.data-v-edf95388 {
		font-size: 40rpx;
}
.title-text.data-v-edf95388 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
}
.search-container.data-v-edf95388 {
		margin-bottom: 30rpx;
}
.search-input-wrapper.data-v-edf95388 {
		display: flex;
		align-items: center;
		background: #f8f9fa;
		border-radius: 50rpx;
		padding: 0 20rpx;
		border: 2rpx solid #e9ecef;
		transition: all 0.3s ease;
}
.search-input-wrapper.data-v-edf95388:focus-within {
		border-color: #667eea;
		background: white;
		box-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);
}
.search-input.data-v-edf95388 {
		flex: 1;
		height: 80rpx;
		font-size: 28rpx;
		color: #333;
		background: transparent;
		border: none;
		outline: none;
}
.search-btn.data-v-edf95388 {
		width: 60rpx;
		height: 60rpx;
		background: linear-gradient(135deg, #667eea, #764ba2);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
}
.search-btn.data-v-edf95388:active {
		transform: scale(0.95);
}
.search-icon.data-v-edf95388 {
		font-size: 28rpx;
		color: white;
}

	/* 高级检索切换按钮 */
.advanced-toggle-btn.data-v-edf95388 {
		width: 60rpx;
		height: 60rpx;
		background: #6c757d;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
		transition: all 0.3s ease;
}
.advanced-toggle-btn.data-v-edf95388:active {
		transform: scale(0.95);
		background: #5a6268;
}
.toggle-icon.data-v-edf95388 {
		font-size: 24rpx;
		color: white;
}

	/* 高级检索框 */
.advanced-search.data-v-edf95388 {
		margin-top: 30rpx;
		padding: 30rpx;
		background: #f8f9fa;
		border-radius: 16rpx;
		border: 1rpx solid #e9ecef;
		animation: slideDown-edf95388 0.3s ease;
}
@keyframes slideDown-edf95388 {
from {
			opacity: 0;
			transform: translateY(-20rpx);
}
to {
			opacity: 1;
			transform: translateY(0);
}
}
.advanced-title.data-v-edf95388 {
		display: flex;
		align-items: center;
		gap: 10rpx;
		margin-bottom: 30rpx;
		padding-bottom: 15rpx;
		border-bottom: 1rpx solid #dee2e6;
}
.advanced-icon.data-v-edf95388 {
		font-size: 28rpx;
		color: #667eea;
}
.advanced-text.data-v-edf95388 {
		font-size: 28rpx;
		font-weight: bold;
		color: #495057;
}

	/* 筛选项 */
.filter-item.data-v-edf95388 {
		margin-bottom: 25rpx;
}
.filter-label.data-v-edf95388 {
		display: block;
		font-size: 26rpx;
		color: #495057;
		margin-bottom: 10rpx;
		font-weight: 500;
}
.filter-input-wrapper.data-v-edf95388 {
		display: flex;
		align-items: center;
		background: white;
		border-radius: 12rpx;
		border: 1rpx solid #ced4da;
		padding: 0 20rpx;
		transition: all 0.3s ease;
}
.filter-input-wrapper.data-v-edf95388:focus-within {
		border-color: #667eea;
		box-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);
}
.filter-input.data-v-edf95388 {
		flex: 1;
		height: 70rpx;
		font-size: 26rpx;
		color: #495057;
		background: transparent;
		border: none;
		outline: none;
}
.clear-btn.data-v-edf95388 {
		width: 40rpx;
		height: 40rpx;
		background: #dc3545;
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;
		margin-left: 10rpx;
		transition: all 0.3s ease;
}
.clear-btn.data-v-edf95388:active {
		transform: scale(0.9);
		background: #c82333;
}
.clear-icon.data-v-edf95388 {
		font-size: 20rpx;
		color: white;
}

	/* 日期范围 */
.date-range-wrapper.data-v-edf95388 {
		display: flex;
		align-items: center;
		gap: 15rpx;
}
.date-input-wrapper.data-v-edf95388 {
		flex: 1;
}
.date-input.data-v-edf95388 {
		display: flex;
		align-items: center;
		justify-content: space-between;
		background: white;
		border-radius: 12rpx;
		border: 1rpx solid #ced4da;
		padding: 20rpx;
		height: 70rpx;
		transition: all 0.3s ease;
}
.date-input.data-v-edf95388:active {
		border-color: #667eea;
		background: #f8f9ff;
}
.date-text.data-v-edf95388 {
		font-size: 26rpx;
		color: #495057;
}
.date-icon.data-v-edf95388 {
		font-size: 24rpx;
		color: #6c757d;
}
.date-separator.data-v-edf95388 {
		font-size: 24rpx;
		color: #6c757d;
		font-weight: 500;
}

	/* 高级检索操作按钮 */
.advanced-actions.data-v-edf95388 {
		display: flex;
		gap: 15rpx;
		margin-top: 30rpx;
		padding-top: 20rpx;
		border-top: 1rpx solid #dee2e6;
}
.action-btn.data-v-edf95388 {
		flex: 1;
		height: 70rpx;
		border-radius: 12rpx;
		border: none;
		display: flex;
		align-items: center;
		justify-content: center;
		gap: 6rpx;
		font-size: 24rpx;
		font-weight: 500;
		transition: all 0.3s ease;
}
.reset-btn.data-v-edf95388 {
		background: #6c757d;
		color: white;
}
.reset-btn.data-v-edf95388:active {
		background: #5a6268;
		transform: scale(0.98);
}
.clear-btn.data-v-edf95388 {
		background: #dc3545;
		color: white;
}
.clear-btn.data-v-edf95388:active {
		background: #c82333;
		transform: scale(0.98);
}
.search-btn-advanced.data-v-edf95388 {
		background: #667eea;
		color: white;
}
.search-btn-advanced.data-v-edf95388:active {
		background: #5a67d8;
		transform: scale(0.98);
}
.btn-icon.data-v-edf95388 {
		font-size: 24rpx;
}
.btn-text.data-v-edf95388 {
		font-size: 26rpx;
}

	/* 欢迎卡片 */
.welcome-card.data-v-edf95388 {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		display: flex;
		align-items: center;
		gap: 30rpx;
}
.welcome-icon.data-v-edf95388 {
		font-size: 80rpx;
}
.welcome-text.data-v-edf95388 {
		flex: 1;
}
.welcome-title.data-v-edf95388 {
		font-size: 36rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 10rpx;
}
.welcome-desc.data-v-edf95388 {
		font-size: 28rpx;
		color: #666;
		display: block;
}

	/* 功能区域 */
.function-area.data-v-edf95388 {
		display: flex;
		flex-direction: column;
		gap: 20rpx;
}
.function-card.data-v-edf95388 {
		background: white;
		border-radius: 16rpx;
		padding: 30rpx;
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);
		display: flex;
		align-items: center;
		gap: 25rpx;
		transition: all 0.3s ease;
}
.function-card.data-v-edf95388:active {
		transform: scale(0.98);
		box-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);
}
.card-icon.data-v-edf95388 {
		font-size: 60rpx;
		width: 80rpx;
		text-align: center;
}
.card-content.data-v-edf95388 {
		flex: 1;
}
.card-title.data-v-edf95388 {
		font-size: 32rpx;
		font-weight: bold;
		color: #333;
		display: block;
		margin-bottom: 8rpx;
}
.card-desc.data-v-edf95388 {
		font-size: 26rpx;
		color: #666;
		display: block;
}
.card-status.data-v-edf95388 {
		font-size: 24rpx;
		color: #999;
		background: #f0f0f0;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
}

	/* 订单列表样式 */
.order-list.data-v-edf95388 {
		padding: 0;
}
.order-item.data-v-edf95388 {
		background: white;
		border-radius: 20rpx;
		padding: 40rpx;
		margin-bottom: 30rpx;
		box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
		transition: all 0.3s ease;
		border: 1rpx solid #f0f0f0;
}
.order-item.data-v-edf95388:active {
		transform: scale(0.98);
		box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);
}
.order-header.data-v-edf95388 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-bottom: 25rpx;
		padding-bottom: 20rpx;
		border-bottom: 2rpx solid #f8f9fa;
}
.order-number.data-v-edf95388 {
		font-size: 36rpx;
		font-weight: bold;
		color: #2c3e50;
		letter-spacing: 1rpx;
}
.order-status-container.data-v-edf95388 {
		display: flex;
		flex-direction: column;
		align-items: flex-end;
		gap: 8rpx;
}
.image-count.data-v-edf95388 {
		font-size: 24rpx;
		color: #666;
		background: #e8f4fd;
		padding: 8rpx 16rpx;
		border-radius: 20rpx;
		border: 1rpx solid #d1ecf1;
}
.shipping-status.data-v-edf95388 {
		font-size: 22rpx;
		font-weight: bold;
		padding: 6rpx 12rpx;
		border-radius: 16rpx;
		text-align: center;
		min-width: 80rpx;
}
.status-shipped.data-v-edf95388 {
		background: #d4edda;
		color: #155724;
		border: 1rpx solid #c3e6cb;
}
.status-pending.data-v-edf95388 {
		background: #fff3cd;
		color: #856404;
		border: 1rpx solid #ffeaa7;
}
.status-overdue.data-v-edf95388 {
		background: #f8d7da;
		color: #721c24;
		border: 1rpx solid #f5c6cb;
}
.status-notified.data-v-edf95388 {
		background: #cce5ff;
		color: #0066cc;
		border: 1rpx solid #99ccff;
}
.status-unknown.data-v-edf95388 {
		background: #e2e3e5;
		color: #383d41;
		border: 1rpx solid #d6d8db;
}
.delay-days.data-v-edf95388 {
		font-size: 20rpx;
		margin-left: 4rpx;
}
.order-info.data-v-edf95388 {
		margin-bottom: 20rpx;
}
.order-info .info-item.data-v-edf95388 {
		display: block;
		font-size: 30rpx;
		color: #555;
		line-height: 2.2;
		margin-bottom: 8rpx;
		padding-left: 20rpx;
		position: relative;
}
.order-info .info-item.data-v-edf95388:before {
		content: "•";
		position: absolute;
		left: 0;
		color: #667eea;
		font-weight: bold;
}
.order-footer.data-v-edf95388 {
		display: flex;
		justify-content: space-between;
		align-items: center;
		margin-top: 25rpx;
		padding-top: 20rpx;
		border-top: 2rpx solid #f8f9fa;
}
.order-date.data-v-edf95388 {
		font-size: 28rpx;
		color: #7f8c8d;
		font-weight: 500;
}
.order-company.data-v-edf95388 {
		font-size: 28rpx;
		color: #667eea;
		font-weight: 500;
		background: #f8f9ff;
		padding: 8rpx 16rpx;
		border-radius: 15rpx;
}

	/* 搜索结果样式 */
.results-header.data-v-edf95388 {
		padding: 20rpx 30rpx 10rpx;
}
.results-title.data-v-edf95388 {
		font-size: 28rpx;
		font-weight: bold;
		color: #333;
}

	/* 底部样式 */
.footer.data-v-edf95388 {
		padding: 30rpx;
		text-align: center;
		background: white;
		border-top: 1rpx solid #eee;
}
.footer-text.data-v-edf95388 {
		font-size: 24rpx;
		color: #999;
}



/**
 * 延迟发货检查服务
 * 定期检查订单是否延迟发货，并发送提醒短信
 */

const SMSQueueService = require('./smsQueueService');
const { query } = require('../config/database');

class DelayedShippingChecker {
    constructor(smsQueueService = null) {
        // 支持依赖注入，避免重复创建实例
        this.smsQueueService = smsQueueService || new SMSQueueService();
        this.isRunning = false;
        this.checkInterval = 60 * 60 * 1000; // 1小时检查一次
        this.delayThresholds = [3, 5, 7]; // 延迟天数阈值

        // 只在没有注入依赖时输出日志
        if (!smsQueueService) {
            console.log('⏰ 延迟发货检查服务初始化完成');
        }
    }

    /**
     * 启动延迟发货检查
     */
    start() {
        if (this.isRunning) {
            console.log('⚠️ 延迟发货检查已在运行中');
            return;
        }

        this.isRunning = true;
        console.log('🚀 启动延迟发货检查服务');

        // 立即执行一次检查
        this.checkDelayedOrders();

        // 设置定时检查
        this.checkTimer = setInterval(() => {
            this.checkDelayedOrders();
        }, this.checkInterval);
    }

    /**
     * 停止延迟发货检查
     */
    stop() {
        if (!this.isRunning) {
            console.log('⚠️ 延迟发货检查未在运行');
            return;
        }

        this.isRunning = false;
        if (this.checkTimer) {
            clearInterval(this.checkTimer);
            this.checkTimer = null;
        }
        console.log('⏹️ 延迟发货检查服务已停止');
    }

    /**
     * 检查延迟发货的订单
     */
    async checkDelayedOrders() {
        try {
            console.log('🔍 开始检查延迟发货订单...');

            // 获取所有未发货的订单
            const unshippedOrders = await this.getUnshippedOrders();
            
            if (unshippedOrders.length === 0) {
                console.log('✅ 没有发现延迟发货的订单');
                return;
            }

            console.log(`📋 发现 ${unshippedOrders.length} 个未发货订单，开始检查延迟情况`);

            let notificationCount = 0;

            for (const order of unshippedOrders) {
                const delayDays = this.calculateDelayDays(order.order_created_at);
                
                // 更新延迟天数
                await this.updateDelayDays(order.order_no, order.factory_name, delayDays);

                // 检查是否需要发送通知
                for (const threshold of this.delayThresholds) {
                    if (delayDays >= threshold) {
                        const shouldSend = await this.shouldSendNotification(order, threshold);
                        if (shouldSend) {
                            await this.sendDelayNotification(order, threshold);
                            await this.markNotificationSent(order.order_no, order.factory_name, threshold);
                            notificationCount++;
                        }
                    }
                }
            }

            if (notificationCount > 0) {
                console.log(`✅ 延迟发货检查完成，发送了 ${notificationCount} 个通知`);
            }

        } catch (error) {
            console.error('❌ 检查延迟发货订单失败:', error);
        }
    }

    /**
     * 获取未发货的订单
     * @returns {Promise<Array>} 未发货订单列表
     */
    async getUnshippedOrders() {
        try {
            const sql = `
                SELECT
                    dst.order_no,
                    dst.factory_name,
                    dst.follower_name,
                    dst.order_created_at,
                    dst.delay_days,
                    dst.notification_3_sent,
                    dst.notification_5_sent,
                    dst.notification_7_sent,
                    fl.phone,
                    fl.allowed
                FROM delayed_shipping_tracking dst
                LEFT JOIN follower_login fl ON dst.follower_name = fl.follower_name AND fl.status = 1
                WHERE dst.is_shipped = 0
                AND fl.phone IS NOT NULL
                AND fl.allowed = 1
                AND DATEDIFF(NOW(), dst.order_created_at) >= 3
                ORDER BY dst.order_created_at ASC
            `;
            
            return await query(sql);
        } catch (error) {
            console.error('❌ 获取未发货订单失败:', error);
            return [];
        }
    }

    /**
     * 计算延迟天数
     * @param {Date|string} orderCreatedAt 订单创建时间
     * @returns {number} 延迟天数
     */
    calculateDelayDays(orderCreatedAt) {
        const created = new Date(orderCreatedAt);
        const now = new Date();
        const diffTime = now - created;
        return Math.floor(diffTime / (1000 * 60 * 60 * 24));
    }

    /**
     * 更新延迟天数
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @param {number} delayDays 延迟天数
     */
    async updateDelayDays(orderNo, factoryName, delayDays) {
        try {
            const sql = `
                UPDATE delayed_shipping_tracking 
                SET delay_days = ?, last_check_at = NOW(), updated_at = NOW()
                WHERE order_no = ? AND factory_name = ?
            `;
            
            await query(sql, [delayDays, orderNo, factoryName]);
        } catch (error) {
            console.error('❌ 更新延迟天数失败:', error);
        }
    }

    /**
     * 检查是否应该发送通知
     * @param {Object} order 订单信息
     * @param {number} threshold 延迟天数阈值
     * @returns {Promise<boolean>} 是否应该发送
     */
    async shouldSendNotification(order, threshold) {
        try {
            // 检查是否已经发送过该阈值的通知
            const fieldName = `notification_${threshold}_sent`;
            return order[fieldName] === 0;
        } catch (error) {
            console.error('❌ 检查通知发送状态失败:', error);
            return false;
        }
    }

    /**
     * 发送延迟通知
     * @param {Object} order 订单信息
     * @param {number} delayDays 延迟天数
     */
    async sendDelayNotification(order, delayDays) {
        try {
            console.log(`📤 发送延迟发货通知: ${order.order_no} (${delayDays}天)`);

            // 1. 发送给跟单员
            const followerSuccess = await this.smsQueueService.addDelayedShippingNotification(
                order.order_no,
                order.factory_name,
                order.follower_name,
                delayDays
            );

            if (followerSuccess) {
                console.log(`✅ 跟单员延迟发货通知已添加到队列: ${order.order_no} (${delayDays}天) -> ${order.follower_name}`);
            } else {
                console.log(`❌ 跟单员延迟发货通知添加失败: ${order.order_no} (${delayDays}天) -> ${order.follower_name}`);
            }

            // 2. 查找并发送给采购员
            await this.sendDelayNotificationToPurchaser(order, delayDays);

        } catch (error) {
            console.error('❌ 发送延迟通知失败:', error);
        }
    }

    /**
     * 发送延迟通知给采购员
     * @param {Object} order 订单信息
     * @param {number} delayDays 延迟天数
     */
    async sendDelayNotificationToPurchaser(order, delayDays) {
        try {
            // 查找对应的采购员
            const purchaserSql = `
                SELECT purchaser
                FROM caigou_order
                WHERE order_id = ?
                LIMIT 1
            `;

            const purchaserResult = await query(purchaserSql, [order.order_no]);

            if (purchaserResult.length === 0 || !purchaserResult[0].purchaser) {
                console.log(`⚠️ 未找到订单 ${order.order_no} 对应的采购员`);
                return;
            }

            const purchaser = purchaserResult[0].purchaser;

            // 清理姓名进行比较
            const cleanFollower = this.cleanFollowerName(order.follower_name);
            const cleanPurchaser = this.cleanFollowerName(purchaser);

            // 如果采购员和跟单员是同一人，则不重复发送
            if (cleanFollower === cleanPurchaser) {
                console.log(`⚠️ 采购员和跟单员为同一人，跳过重复通知: ${cleanPurchaser}`);
                return;
            }

            // 发送给采购员
            const purchaserSuccess = await this.smsQueueService.addDelayedShippingNotification(
                order.order_no,
                order.factory_name,
                purchaser,
                delayDays
            );

            if (purchaserSuccess) {
                console.log(`✅ 采购员延迟发货通知已添加到队列: ${order.order_no} (${delayDays}天) -> ${purchaser}`);
            } else {
                console.log(`❌ 采购员延迟发货通知添加失败: ${order.order_no} (${delayDays}天) -> ${purchaser}`);
            }

        } catch (error) {
            console.error('❌ 发送采购员延迟通知失败:', error);
        }
    }

    /**
     * 清理跟单员姓名（移除手机号等）
     * @param {string} name 原始姓名
     * @returns {string} 清理后的姓名
     */
    cleanFollowerName(name) {
        if (!name) return '';
        // 移除手机号（1开头的11位数字）
        return name.replace(/\s*1[3-9]\d{9}\s*/g, '').trim();
    }

    /**
     * 标记通知已发送
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     * @param {number} threshold 延迟天数阈值
     */
    async markNotificationSent(orderNo, factoryName, threshold) {
        try {
            const fieldName = `notification_${threshold}_sent`;
            const timeFieldName = `notification_${threshold}_sent_at`;
            
            const sql = `
                UPDATE delayed_shipping_tracking 
                SET ${fieldName} = 1, ${timeFieldName} = NOW(), updated_at = NOW()
                WHERE order_no = ? AND factory_name = ?
            `;
            
            await query(sql, [orderNo, factoryName]);
        } catch (error) {
            console.error('❌ 标记通知发送状态失败:', error);
        }
    }

    /**
     * 手动检查特定订单
     * @param {string} orderNo 订单号
     * @param {string} factoryName 工厂名称
     */
    async checkSpecificOrder(orderNo, factoryName) {
        try {


            const sql = `
                SELECT
                    dst.order_no,
                    dst.factory_name,
                    dst.follower_name,
                    dst.order_created_at,
                    dst.delay_days,
                    dst.notification_3_sent,
                    dst.notification_5_sent,
                    dst.notification_7_sent,
                    dst.is_shipped,
                    fl.phone,
                    fl.allowed
                FROM delayed_shipping_tracking dst
                LEFT JOIN follower_login fl ON dst.follower_name = fl.follower_name AND fl.status = 1
                WHERE dst.order_no = ? AND dst.factory_name = ?
            `;

            const result = await query(sql, [orderNo, factoryName]);
            
            if (result.length === 0) {
                console.log(`⚠️ 未找到订单跟踪记录: ${orderNo}`);
                return null;
            }

            const order = result[0];
            
            if (order.is_shipped) {
                console.log(`✅ 订单已发货: ${orderNo}`);
                return order;
            }

            if (!order.phone) {
                console.log(`⚠️ 跟单员未配置手机号: ${order.follower_name}`);
                return order;
            }

            if (!order.allowed || order.allowed === 0) {
                console.log(`⚠️ 跟单员已关闭短信通知: ${order.follower_name}`);
                return order;
            }

            const delayDays = this.calculateDelayDays(order.order_created_at);
            
            // 更新延迟天数
            await this.updateDelayDays(orderNo, factoryName, delayDays);

            console.log(`📊 订单 ${orderNo} 延迟 ${delayDays} 天`);

            // 检查并发送通知
            for (const threshold of this.delayThresholds) {
                if (delayDays >= threshold) {
                    const shouldSend = await this.shouldSendNotification(order, threshold);
                    if (shouldSend) {
                        await this.sendDelayNotification(order, threshold);
                        await this.markNotificationSent(orderNo, factoryName, threshold);
                        console.log(`📤 已发送 ${threshold} 天延迟通知: ${orderNo}`);
                    } else {
                        console.log(`⚠️ ${threshold} 天延迟通知已发送过: ${orderNo}`);
                    }
                }
            }

            return { ...order, current_delay_days: delayDays };

        } catch (error) {
            console.error('❌ 检查特定订单失败:', error);
            return null;
        }
    }

    /**
     * 获取延迟发货统计
     * @returns {Promise<Object>} 统计信息
     */
    async getDelayedShippingStats() {
        try {
            const sql = `
                SELECT 
                    COUNT(*) as total_orders,
                    SUM(CASE WHEN is_shipped = 0 THEN 1 ELSE 0 END) as unshipped_orders,
                    SUM(CASE WHEN is_shipped = 0 AND delay_days >= 3 THEN 1 ELSE 0 END) as delayed_3_days,
                    SUM(CASE WHEN is_shipped = 0 AND delay_days >= 5 THEN 1 ELSE 0 END) as delayed_5_days,
                    SUM(CASE WHEN is_shipped = 0 AND delay_days >= 7 THEN 1 ELSE 0 END) as delayed_7_days,
                    SUM(notification_3_sent) as notifications_3_sent,
                    SUM(notification_5_sent) as notifications_5_sent,
                    SUM(notification_7_sent) as notifications_7_sent
                FROM delayed_shipping_tracking
            `;
            
            const result = await query(sql);
            return result[0] || {};
        } catch (error) {
            console.error('❌ 获取延迟发货统计失败:', error);
            return {};
        }
    }
}

module.exports = DelayedShippingChecker;

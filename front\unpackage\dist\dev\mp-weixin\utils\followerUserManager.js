"use strict";
const common_vendor = require("../common/vendor.js");
const FOLLOWER_USER_STORAGE_KEY = "follower_user_info";
const FOLLOWER_TOKEN_EXPIRY_KEY = "follower_token_expiry";
class FollowerUserManager {
  constructor() {
    this.userInfo = null;
    this.isLoggedIn = false;
    this.init();
  }
  /**
   * 初始化用户管理器
   */
  init() {
    try {
      const storedUserInfo = common_vendor.index.getStorageSync(FOLLOWER_USER_STORAGE_KEY);
      const tokenExpiry = common_vendor.index.getStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY);
      if (storedUserInfo && tokenExpiry) {
        const expiryTime = new Date(tokenExpiry);
        const now = /* @__PURE__ */ new Date();
        if (expiryTime > now) {
          this.userInfo = storedUserInfo;
          this.isLoggedIn = true;
          common_vendor.index.__f__("log", "at utils/followerUserManager.js:34", "✅ 跟单员用户登录状态已恢复:", storedUserInfo.username);
        } else {
          common_vendor.index.__f__("log", "at utils/followerUserManager.js:37", "⚠️ 跟单员Token已过期，清除登录状态");
          this.clearStorage();
        }
      }
      this.updateGlobalState();
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:45", "❌ 初始化跟单员用户管理器失败:", error);
      this.clearStorage();
    }
  }
  /**
   * 跟单员用户登录
   * @param {Object} loginData 登录返回的数据
   */
  login(loginData) {
    try {
      const { username, follower_name, position, token, expires_in = 2592e3 } = loginData;
      const expiryTime = new Date(Date.now() + expires_in * 1e3);
      const userInfo = {
        username,
        follower_name,
        position,
        token,
        loginTime: (/* @__PURE__ */ new Date()).toISOString()
      };
      common_vendor.index.setStorageSync(FOLLOWER_USER_STORAGE_KEY, userInfo);
      common_vendor.index.setStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY, expiryTime.toISOString());
      this.userInfo = userInfo;
      this.isLoggedIn = true;
      this.updateGlobalState();
      common_vendor.index.__f__("log", "at utils/followerUserManager.js:80", "✅ 跟单员用户登录状态保存成功:", username);
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:84", "❌ 保存跟单员用户登录状态失败:", error);
      return false;
    }
  }
  /**
   * 用户登出
   */
  logout() {
    try {
      this.clearStorage();
      this.userInfo = null;
      this.isLoggedIn = false;
      this.updateGlobalState();
      common_vendor.index.__f__("log", "at utils/followerUserManager.js:104", "✅ 跟单员用户已登出");
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:107", "❌ 跟单员用户登出失败:", error);
      return false;
    }
  }
  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  checkLoginStatus() {
    try {
      if (!this.isLoggedIn || !this.userInfo) {
        return false;
      }
      const tokenExpiry = common_vendor.index.getStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY);
      if (!tokenExpiry) {
        this.logout();
        return false;
      }
      const expiryTime = new Date(tokenExpiry);
      const now = /* @__PURE__ */ new Date();
      if (expiryTime <= now) {
        common_vendor.index.__f__("log", "at utils/followerUserManager.js:133", "⚠️ 跟单员Token已过期");
        this.logout();
        return false;
      }
      return true;
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:140", "❌ 检查跟单员登录状态失败:", error);
      this.logout();
      return false;
    }
  }
  /**
   * 获取用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    if (this.checkLoginStatus()) {
      return this.userInfo;
    }
    return null;
  }
  /**
   * 获取Token
   * @returns {string|null} Token
   */
  getToken() {
    if (this.checkLoginStatus()) {
      return this.userInfo.token;
    }
    return null;
  }
  /**
   * 获取用户名
   * @returns {string|null} 用户名
   */
  getUsername() {
    if (this.checkLoginStatus()) {
      return this.userInfo.username;
    }
    return null;
  }
  /**
   * 获取跟单员姓名
   * @returns {string|null} 跟单员姓名
   */
  getFollowerName() {
    if (this.checkLoginStatus()) {
      return this.userInfo.follower_name;
    }
    return null;
  }
  /**
   * 创建认证请求配置
   * @param {Object} config 请求配置
   */
  createAuthRequest(config = {}) {
    const token = this.getToken();
    return {
      ...config,
      header: {
        "Content-Type": "application/json",
        "Authorization": token ? `Bearer ${token}` : "",
        ...config.header
      }
    };
  }
  /**
   * 清除本地存储
   */
  clearStorage() {
    try {
      common_vendor.index.removeStorageSync(FOLLOWER_USER_STORAGE_KEY);
      common_vendor.index.removeStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY);
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:215", "❌ 清除跟单员用户存储失败:", error);
    }
  }
  /**
   * 更新全局状态
   */
  updateGlobalState() {
    try {
      if (typeof getApp === "function") {
        const app = getApp();
        if (app.globalData) {
          app.globalData.followerUserManager = this;
          app.globalData.isFollowerLoggedIn = this.isLoggedIn;
          app.globalData.followerUserInfo = this.userInfo;
        }
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:234", "❌ 更新跟单员全局状态失败:", error);
    }
  }
  /**
   * 强制刷新登录状态
   */
  refreshLoginStatus() {
    this.init();
  }
  /**
   * 修改密码
   * @param {string} currentPassword 当前密码
   * @param {string} newPassword 新密码
   * @returns {Promise<Object>} 修改结果
   */
  async changePassword(currentPassword, newPassword) {
    try {
      if (!this.checkLoginStatus()) {
        throw new Error("用户未登录");
      }
      const token = this.getToken();
      if (!token) {
        throw new Error("认证token不存在");
      }
      const response = await new Promise((resolve, reject) => {
        common_vendor.index.request({
          url: "http://localhost:3001/api/auth/follower-change-password",
          method: "POST",
          data: {
            currentPassword: currentPassword.trim(),
            newPassword: newPassword.trim()
          },
          header: {
            "Content-Type": "application/json",
            "Authorization": `Bearer ${token}`
          },
          timeout: 1e4,
          success: (res) => {
            resolve(res.data);
          },
          fail: (err) => {
            common_vendor.index.__f__("error", "at utils/followerUserManager.js:280", "修改密码请求失败:", err);
            reject(new Error("网络连接失败"));
          }
        });
      });
      if (response.success) {
        common_vendor.index.__f__("log", "at utils/followerUserManager.js:287", "✅ 密码修改成功");
        return {
          success: true,
          message: response.message || "密码修改成功"
        };
      } else {
        common_vendor.index.__f__("error", "at utils/followerUserManager.js:293", "❌ 密码修改失败:", response.message);
        return {
          success: false,
          message: response.message || "密码修改失败"
        };
      }
    } catch (error) {
      common_vendor.index.__f__("error", "at utils/followerUserManager.js:301", "❌ 修改密码异常:", error);
      return {
        success: false,
        message: error.message || "修改密码失败"
      };
    }
  }
}
const followerUserManager = new FollowerUserManager();
exports.followerUserManager = followerUserManager;
//# sourceMappingURL=../../.sourcemap/mp-weixin/utils/followerUserManager.js.map

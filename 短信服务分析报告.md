# 📱 短信服务全流程分析报告

## 🏗️ 短信服务架构

短信服务采用**分层架构**设计，包含以下核心组件：

1. **阿里云短信服务** (`AliyunSMSService`) - 底层短信发送
2. **短信通知服务** (`SMSNotificationService`) - 业务逻辑处理
3. **短信队列服务** (`SMSQueueService`) - 异步队列处理
4. **延迟发货检查服务** (`DelayedShippingChecker`) - 定时检查
5. **重复检查服务** (`DuplicateCheckService`) - 防重复发送
6. **服务管理器** (`ServiceManager`) - 单例管理

## 📋 短信模板配置

系统配置了**3种短信模板**：

### 1. 订单生成通知
- **模板代码**: `SMS_493305115`
- **签名**: 智联金
- **描述**: 坯布订单生成消息
- **参数**:
  - `follower`: 跟单员姓名
  - `orderNo`: 订单号
  - `customerName`: 工厂名称
  - `orderTime`: 订单创建时间

### 2. 延迟发货通知
- **模板代码**: `SMS_493420157`
- **签名**: 智联金
- **描述**: 坯布发货超时通知
- **参数**:
  - `follower`: 跟单员姓名
  - `orderNo`: 订单号
  - `delayDays`: 延迟天数
  - `customerName`: 工厂名称
  - `orderTime`: 订单创建时间

### 3. 订单发货通知
- **模板代码**: `SMS_493260128`
- **签名**: 智联金
- **描述**: 坯布发货通知
- **参数**:
  - `follower`: 跟单员姓名
  - `orderNo`: 订单号
  - `customerName`: 工厂名称
  - `orderTime`: 订单创建时间

## 👥 通知对象

### 跟单员用户
- **数据表**: `follower_login`
- **关键字段**:
  - `follower_name`: 跟单员姓名
  - `phone`: 手机号码
  - `allowed`: 是否允许接收短信通知 (1=允许, 0=不允许)
  - `status`: 用户状态 (1=启用, 0=禁用)
  - `position`: 用户身份 (1=跟单员, 2=订坯人员, 3=两种身份)

### 通知条件
- 跟单员必须**已启用** (`status = 1`)
- 跟单员必须**配置手机号** (`phone` 不为空)
- 跟单员必须**允许接收通知** (`allowed = 1`)

## 🔄 短信触发流程

### 1. 订单生成通知触发

#### 触发条件
- 当 `shipping_detail` 表插入新记录时
- 通过**数据库触发器**自动触发

#### 触发器逻辑
```sql
-- 触发器: tr_shipping_detail_sms_final
AFTER INSERT ON shipping_detail FOR EACH ROW
BEGIN
    -- 检查跟单员信息
    IF NEW.follower IS NOT NULL AND TRIM(NEW.follower) != '' THEN
        -- 获取跟单员手机号和通知开关
        SELECT phone, allowed INTO v_phone, v_allowed
        FROM follower_login
        WHERE follower_name = NEW.follower AND status = 1;
        
        -- 如果允许通知且有手机号
        IF v_allowed = 1 AND v_phone IS NOT NULL THEN
            -- 检查是否已发送过
            IF NOT EXISTS (重复检查逻辑) THEN
                -- 插入短信队列
                INSERT INTO sms_notification_queue (...);
                -- 创建延迟发货跟踪记录
                INSERT INTO delayed_shipping_tracking (...);
            END IF;
        END IF;
    END IF;
END
```

### 2. 延迟发货通知触发

#### 触发条件
- **定时检查**: 每1小时检查一次
- **延迟阈值**: 3天、5天、7天

#### 检查逻辑
```javascript
// DelayedShippingChecker.checkDelayedOrders()
const delayThresholds = [3, 5, 7]; // 延迟天数阈值

// 查询未发货且超过阈值的订单
const sql = `
    SELECT * FROM delayed_shipping_tracking 
    WHERE is_shipped = 0 
    AND DATEDIFF(NOW(), order_created_at) >= ?
    AND notification_${delayDays}_sent = 0
`;

// 为每个延迟订单发送通知
for (const delayDays of delayThresholds) {
    const delayedOrders = await query(sql, [delayDays]);
    for (const order of delayedOrders) {
        await this.sendDelayNotification(order, delayDays);
    }
}
```

### 3. 订单发货通知触发

#### 触发条件
- 当 `shipping_upload` 表插入新记录时
- 表示订单已实际发货

#### 触发逻辑
- 手动调用 `sendOrderShippedNotification` 方法
- 或通过API接口 `/api/sms/send-order-shipped` 触发

## 📤 短信发送流程

### 1. 队列处理机制

#### 队列服务启动
```javascript
// 启动队列处理服务
smsQueueService.startProcessing();
// 每5秒检查一次队列
setInterval(() => {
    this.processQueue();
}, 5000);
```

#### 队列处理流程
1. **获取待处理任务**: 从 `sms_notification_queue` 表查询 `status = 'pending'` 的记录
2. **更新任务状态**: 设置为 `'processing'`
3. **发送短信**: 调用阿里云短信API
4. **更新结果**: 根据发送结果更新状态为 `'completed'` 或 `'failed'`
5. **记录日志**: 在 `sms_logs` 表记录发送结果

### 2. 重复发送检查

#### 检查机制
- **订单生成通知**: 检查 `template_code + order_no + follower_name`
- **延迟发货通知**: 检查 `template_code + order_no + follower_name + delay_days`
- **订单发货通知**: 检查 `template_code + order_no + follower_name`

#### 检查逻辑
```javascript
// DuplicateCheckService.checkOrderCreatedDuplicate()
const sql = `
    SELECT COUNT(*) as count FROM sms_logs 
    WHERE template_code = ? AND order_no = ? AND follower_name = ?
    AND send_status = 'success'
`;
const result = await query(sql, [templateCode, orderNo, followerName]);
return result[0].count > 0;
```

### 3. 阿里云短信发送

#### 发送参数
```javascript
const params = {
    PhoneNumbers: phoneNumbers,        // 手机号
    SignName: signName,               // 短信签名: "智联金"
    TemplateCode: templateCode,       // 模板代码
    TemplateParam: JSON.stringify(templateParams) // 模板参数
};
```

#### 发送结果处理
- **成功**: `result.Code === 'OK'`
- **失败**: 记录错误信息，支持重试机制（最多3次）

## 📊 数据库表结构

### 1. 短信队列表 (`sms_notification_queue`)
```sql
CREATE TABLE sms_notification_queue (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(100),
    factory_name VARCHAR(100),
    follower_name VARCHAR(100),
    phone_number VARCHAR(20),
    notification_type ENUM('order_created','order_shipped','delayed_shipping'),
    template_code VARCHAR(50),
    template_params JSON,
    status ENUM('pending','processing','completed','failed'),
    retry_count INT DEFAULT 0,
    error_msg TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

### 2. 短信日志表 (`sms_logs`)
```sql
CREATE TABLE sms_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    phone_numbers VARCHAR(500),
    template_code VARCHAR(50),
    order_no VARCHAR(100),
    follower_name VARCHAR(100),
    notification_type VARCHAR(50),
    send_status ENUM('success','failed'),
    response_message TEXT,
    send_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

### 3. 延迟发货跟踪表 (`delayed_shipping_tracking`)
```sql
CREATE TABLE delayed_shipping_tracking (
    id INT AUTO_INCREMENT PRIMARY KEY,
    order_no VARCHAR(100),
    factory_name VARCHAR(100),
    follower_name VARCHAR(100),
    order_created_at TIMESTAMP,
    is_shipped TINYINT DEFAULT 0,
    shipped_at TIMESTAMP NULL,
    delay_days INT DEFAULT 0,
    notification_3_sent TINYINT DEFAULT 0,
    notification_5_sent TINYINT DEFAULT 0,
    notification_7_sent TINYINT DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);
```

## ⏰ 定时任务配置

### 1. 延迟发货检查
- **频率**: 每1小时执行一次
- **实现**: `DelayedShippingChecker.start()`
- **检查内容**: 未发货订单的延迟天数

### 2. 队列清理任务
- **频率**: 每月1号凌晨2点执行
- **实现**: `node-cron` 定时任务
- **清理内容**: 已完成的短信任务记录

```javascript
// app.js 中的定时任务配置
const schedule = require('node-cron');
schedule.schedule('0 2 1 * *', () => {
    smsQueueService.cleanupCompletedTasks();
});
```

## 🔧 服务启动流程

### 1. 服务初始化
```javascript
// 在 app.js 中初始化服务管理器
const serviceManager = require('./utils/serviceManager');
app.serviceManager = serviceManager;

// 启动所有短信服务
serviceManager.startServices();
```

### 2. 服务依赖关系
```
ServiceManager
├── AliyunSMSService (阿里云短信API)
├── DuplicateCheckService (重复检查)
├── SMSNotificationService (业务逻辑)
│   └── 依赖: AliyunSMSService
├── SMSQueueService (队列处理)
│   └── 依赖: SMSNotificationService
└── DelayedShippingChecker (延迟检查)
    └── 依赖: SMSQueueService
```

## 📱 短信内容示例

### 订单生成通知
```
【智联金】${follower}，您有新的坯布订单：${orderNo}，客户：${customerName}，下单时间：${orderTime}，请及时跟进。
```

### 延迟发货通知
```
【智联金】${follower}，订单${orderNo}已延迟${delayDays}天未发货，客户：${customerName}，下单时间：${orderTime}，请尽快处理。
```

### 订单发货通知
```
【智联金】${follower}，订单${orderNo}已发货，客户：${customerName}，下单时间：${orderTime}，请通知客户。
```

## 🛡️ 安全和可靠性

### 1. 防重复发送
- 数据库级别的重复检查
- 基于订单号、跟单员、通知类型的唯一性约束

### 2. 错误处理
- 短信发送失败自动重试（最多3次）
- 详细的错误日志记录
- 事务保护确保数据一致性

### 3. 性能优化
- 异步队列处理，避免阻塞主流程
- 批量处理，提高发送效率
- 连接池管理，优化数据库性能

这个短信服务系统设计完善，具有高可靠性、可扩展性和维护性，能够有效支撑工厂订单管理的业务需求。

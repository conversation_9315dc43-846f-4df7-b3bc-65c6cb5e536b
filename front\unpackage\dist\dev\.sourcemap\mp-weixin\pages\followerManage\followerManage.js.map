{"version": 3, "file": "followerManage.js", "sources": ["pages/followerManage/followerManage.vue", "E:/HBuilderX/plugins/uniapp-cli-vite/uniPage:/cGFnZXMvZm9sbG93ZXJNYW5hZ2UvZm9sbG93ZXJNYW5hZ2UudnVl"], "sourcesContent": ["<template>\n\t<view class=\"container\">\n\t\t<!-- 顶部用户信息 -->\n\t\t<view class=\"header\" v-if=\"currentUser\">\n\t\t\t<view class=\"title-section\">\n\t\t\t\t<text class=\"title\">坯布管理</text>\n\t\t\t\t<text class=\"subtitle\">坯布管理工作台</text>\n\t\t\t</view>\n\t\t\t<view class=\"user-section\">\n\t\t\t\t<view class=\"user-info\">\n\t\t\t\t\t<text class=\"user-name\">{{ currentUser.follower_name }}</text>\n\t\t\t\t\t<text class=\"user-dept\">{{ getUserPositionText(currentUser.position) }}</text>\n\t\t\t\t</view>\n\t\t\t\t<button class=\"logout-btn\" @click=\"handleLogout\">\n\t\t\t\t\t<text class=\"logout-icon\">🚪</text>\n\t\t\t\t</button>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 主要内容区域 -->\n\t\t<view class=\"main-content\">\n\t\t\t<!-- 短信通知设置区域 -->\n\t\t\t<view class=\"notification-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-icon\">📱</text>\n\t\t\t\t\t<text class=\"title-text\">短信通知设置</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"notification-card\">\n\t\t\t\t\t<view class=\"card-header\">\n\t\t\t\t\t\t<text class=\"card-title\">短信通知开关</text>\n\t\t\t\t\t\t<switch\n\t\t\t\t\t\t\t:checked=\"notificationSettings.allowed\"\n\t\t\t\t\t\t\t@change=\"onNotificationToggle\"\n\t\t\t\t\t\t\t:disabled=\"!notificationSettings.hasPhone\"\n\t\t\t\t\t\t\tcolor=\"#667eea\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"card-content\">\n\t\t\t\t\t\t<view class=\"phone-section\">\n\t\t\t\t\t\t\t<view class=\"phone-label\">\n\t\t\t\t\t\t\t\t<text class=\"label-text\">手机号码</text>\n\t\t\t\t\t\t\t\t<text class=\"required-mark\" v-if=\"!notificationSettings.hasPhone\">*</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"phone-input-group\">\n\t\t\t\t\t\t\t\t<input\n\t\t\t\t\t\t\t\t\tclass=\"phone-input\"\n\t\t\t\t\t\t\t\t\ttype=\"number\"\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入手机号码\"\n\t\t\t\t\t\t\t\t\tv-model=\"phoneInput\"\n\t\t\t\t\t\t\t\t\t:disabled=\"isUpdatingPhone\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<button\n\t\t\t\t\t\t\t\t\tclass=\"phone-btn\"\n\t\t\t\t\t\t\t\t\t@click=\"updatePhone\"\n\t\t\t\t\t\t\t\t\t:disabled=\"isUpdatingPhone\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{{ notificationSettings.hasPhone ? '修改' : '添加' }}\n\t\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"notification-tip\" v-if=\"!notificationSettings.hasPhone\">\n\t\t\t\t\t\t\t<text class=\"tip-icon\">💡</text>\n\t\t\t\t\t\t\t<text class=\"tip-text\">请先添加手机号码才能开启短信通知</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<view class=\"notification-status\" v-else>\n\t\t\t\t\t\t\t<text class=\"status-icon\">{{ notificationSettings.allowed ? '✅' : '❌' }}</text>\n\t\t\t\t\t\t\t<text class=\"status-text\">\n\t\t\t\t\t\t\t\t短信通知已{{ notificationSettings.allowed ? '开启' : '关闭' }}\n\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 订单查询区域 -->\n\t\t\t<view class=\"search-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-icon\">🔍</text>\n\t\t\t\t\t<text class=\"title-text\">订单查询</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"search-container\">\n\t\t\t\t\t<!-- 基础搜索框 -->\n\t\t\t\t\t<view class=\"search-input-wrapper\">\n\t\t\t\t\t\t<input\n\t\t\t\t\t\t\tclass=\"search-input\"\n\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t\tplaceholder=\"请输入订单号进行查询\"\n\t\t\t\t\t\t\tv-model=\"searchKeyword\"\n\t\t\t\t\t\t\t@input=\"onSearchInput\"\n\t\t\t\t\t\t\t@confirm=\"handleSearch\"\n\t\t\t\t\t\t/>\n\t\t\t\t\t\t<view class=\"search-btn\" @click=\"handleSearch\">\n\t\t\t\t\t\t\t<text class=\"search-icon\">🔍</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<!-- 高级检索切换按钮 -->\n\t\t\t\t\t\t<view class=\"advanced-toggle-btn\" @click=\"toggleAdvancedSearch\">\n\t\t\t\t\t\t\t<text class=\"toggle-icon\">{{ showAdvancedSearch ? '🔼' : '🔽' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<!-- 高级检索框 -->\n\t\t\t\t\t<view class=\"advanced-search\" v-if=\"showAdvancedSearch\">\n\t\t\t\t\t\t<view class=\"advanced-title\">\n\t\t\t\t\t\t\t<text class=\"advanced-icon\">⚙️</text>\n\t\t\t\t\t\t\t<text class=\"advanced-text\">高级检索</text>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 坯布商检索 -->\n\t\t\t\t\t\t<view class=\"filter-item\">\n\t\t\t\t\t\t\t<text class=\"filter-label\">坯布商</text>\n\t\t\t\t\t\t\t<view class=\"filter-input-wrapper\">\n\t\t\t\t\t\t\t\t<input\n\t\t\t\t\t\t\t\t\tclass=\"filter-input\"\n\t\t\t\t\t\t\t\t\ttype=\"text\"\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入坯布商名称\"\n\t\t\t\t\t\t\t\t\tv-model=\"advancedFilters.factoryName\"\n\t\t\t\t\t\t\t\t\t@input=\"onAdvancedFilterChange\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<view class=\"clear-btn\" v-if=\"advancedFilters.factoryName\" @click=\"clearFactoryFilter\">\n\t\t\t\t\t\t\t\t\t<text class=\"clear-icon\">✕</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 日期检索 -->\n\t\t\t\t\t\t<view class=\"filter-item\">\n\t\t\t\t\t\t\t<text class=\"filter-label\">日期范围</text>\n\t\t\t\t\t\t\t<view class=\"date-range-wrapper\">\n\t\t\t\t\t\t\t\t<view class=\"date-input-wrapper\">\n\t\t\t\t\t\t\t\t\t<picker\n\t\t\t\t\t\t\t\t\t\tmode=\"date\"\n\t\t\t\t\t\t\t\t\t\t:value=\"advancedFilters.startDate\"\n\t\t\t\t\t\t\t\t\t\t@change=\"onStartDateChange\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<view class=\"date-input\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"date-text\">{{ advancedFilters.startDate || '开始日期' }}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"date-icon\">📅</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t<text class=\"date-separator\">至</text>\n\t\t\t\t\t\t\t\t<view class=\"date-input-wrapper\">\n\t\t\t\t\t\t\t\t\t<picker\n\t\t\t\t\t\t\t\t\t\tmode=\"date\"\n\t\t\t\t\t\t\t\t\t\t:value=\"advancedFilters.endDate\"\n\t\t\t\t\t\t\t\t\t\t@change=\"onEndDateChange\"\n\t\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t\t<view class=\"date-input\">\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"date-text\">{{ advancedFilters.endDate || '结束日期' }}</text>\n\t\t\t\t\t\t\t\t\t\t\t<text class=\"date-icon\">📅</text>\n\t\t\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t\t\t</picker>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\n\t\t\t\t\t\t<!-- 高级检索操作按钮 -->\n\t\t\t\t\t\t<view class=\"advanced-actions\">\n\t\t\t\t\t\t\t<button class=\"action-btn reset-btn\" @click=\"resetAdvancedFilters\">\n\t\t\t\t\t\t\t\t<text class=\"btn-icon\">🔄</text>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">重置</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<button class=\"action-btn clear-btn\" @click=\"clearSearchResults\">\n\t\t\t\t\t\t\t\t<text class=\"btn-icon\">🗑️</text>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">清空</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t\t<button class=\"action-btn search-btn-advanced\" @click=\"handleAdvancedSearch\">\n\t\t\t\t\t\t\t\t<text class=\"btn-icon\">🔍</text>\n\t\t\t\t\t\t\t\t<text class=\"btn-text\">检索</text>\n\t\t\t\t\t\t\t</button>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 搜索结果 -->\n\t\t\t\t<view class=\"search-results\" v-if=\"searchResults.length > 0\">\n\t\t\t\t\t<view class=\"results-header\">\n\t\t\t\t\t\t<text class=\"results-title\">搜索结果 ({{ searchResults.length }})</text>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"order-item\"\n\t\t\t\t\t\tv-for=\"(order, index) in searchResults\"\n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"viewOrderDetail(order)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"order-header\">\n\t\t\t\t\t\t\t<view class=\"order-number\">{{ order.order_number }}</view>\n\t\t\t\t\t\t\t<view class=\"order-status-container\">\n\t\t\t\t\t\t\t\t<view class=\"image-count\">{{ order.image_count }} 张图片</view>\n\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\tclass=\"shipping-status\"\n\t\t\t\t\t\t\t\t\t:class=\"getShippingStatusClass(order.order_status || order.shipping_status)\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{{ order.order_status || order.shipping_status_text || '未知' }}\n\t\t\t\t\t\t\t\t\t<text v-if=\"order.order_status === '超时' && order.delay_days\" class=\"delay-days\">\n\t\t\t\t\t\t\t\t\t\t({{ order.delay_days }}天)\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t\t<text class=\"info-item\">坯布商: {{ order.supplier || order.factory_name || '未指定' }}</text>\n\t\t\t\t\t\t\t<text class=\"info-item\">品名: {{ order.product_name || '未指定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-footer\">\n\t\t\t\t\t\t\t<text class=\"order-date\">{{ formatDate(order.created_at) }}</text>\n\t\t\t\t\t\t\t<text class=\"order-company\">{{ order.deliver_company || '未指定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 搜索无结果 -->\n\t\t\t\t<view class=\"no-results\" v-if=\"showNoResults\">\n\t\t\t\t\t<text class=\"no-results-icon\">📭</text>\n\t\t\t\t\t<text class=\"no-results-text\">未找到匹配的订单</text>\n\t\t\t\t\t<text class=\"no-results-desc\">请检查订单号是否正确</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<!-- 历史订单区域 -->\n\t\t\t<view class=\"history-section\">\n\t\t\t\t<view class=\"section-title\">\n\t\t\t\t\t<text class=\"title-icon\">📋</text>\n\t\t\t\t\t<text class=\"title-text\">最新订单 ({{ orderHistory.length }})</text>\n\t\t\t\t</view>\n\n\t\t\t\t<view class=\"order-list\" v-if=\"orderHistory.length > 0\">\n\t\t\t\t\t<view\n\t\t\t\t\t\tclass=\"order-item\"\n\t\t\t\t\t\tv-for=\"(order, index) in orderHistory\"\n\t\t\t\t\t\t:key=\"index\"\n\t\t\t\t\t\t@click=\"viewOrderDetail(order)\"\n\t\t\t\t\t>\n\t\t\t\t\t\t<view class=\"order-header\">\n\t\t\t\t\t\t\t<view class=\"order-number\">{{ order.order_number }}</view>\n\t\t\t\t\t\t\t<view class=\"order-status-container\">\n\t\t\t\t\t\t\t\t<view class=\"image-count\">{{ order.image_count }} 张图片</view>\n\t\t\t\t\t\t\t\t<view\n\t\t\t\t\t\t\t\t\tclass=\"shipping-status\"\n\t\t\t\t\t\t\t\t\t:class=\"getShippingStatusClass(order.order_status || order.shipping_status)\"\n\t\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t\t{{ order.order_status || order.shipping_status_text || '未知' }}\n\t\t\t\t\t\t\t\t\t<text v-if=\"order.order_status === '超时' && order.delay_days\" class=\"delay-days\">\n\t\t\t\t\t\t\t\t\t\t({{ order.delay_days }}天)\n\t\t\t\t\t\t\t\t\t</text>\n\t\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-info\">\n\t\t\t\t\t\t\t<text class=\"info-item\">坯布商: {{ order.supplier || order.factory_name || '未指定' }}</text>\n\t\t\t\t\t\t\t<text class=\"info-item\">品名: {{ order.product_name || '未指定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t\t<view class=\"order-footer\">\n\t\t\t\t\t\t\t<text class=\"order-date\">{{ formatDate(order.created_at) }}</text>\n\t\t\t\t\t\t\t<text class=\"order-company\">{{ order.deliver_company || '未指定' }}</text>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 暂无订单 -->\n\t\t\t\t<view class=\"empty-state\" v-if=\"orderHistory.length === 0 && !isLoading\">\n\t\t\t\t\t<text class=\"empty-icon\">📦</text>\n\t\t\t\t\t<text class=\"empty-text\">暂无订单记录</text>\n\t\t\t\t\t<text class=\"empty-desc\">您负责的订单将在这里显示</text>\n\t\t\t\t</view>\n\n\t\t\t\t<!-- 加载状态 -->\n\t\t\t\t<view class=\"loading-state\" v-if=\"isLoading\">\n\t\t\t\t\t<text class=\"loading-icon\">⏳</text>\n\t\t\t\t\t<text class=\"loading-text\">正在加载订单数据...</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\n\t\t<!-- 底部信息 -->\n\t\t<view class=\"footer\">\n\t\t\t<text class=\"footer-text\">跟单管理系统 v1.0.0</text>\n\t\t</view>\n\n\n\t</view>\n</template>\n\n<script>\n\timport followerUserManager from '@/utils/followerUserManager.js';\n\timport warehouseUserManager from '@/utils/warehouseUsers.js';\n\timport urlConfig from '@/utils/urlConfig.js';\n\timport { showSuccess, showError } from '@/utils/helpers.js';\n\timport apiService from '@/utils/apiService.js';\n\n\texport default {\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\tcurrentUser: null,\n\t\t\t\tsearchKeyword: '',\n\t\t\t\tsearchResults: [],\n\t\t\t\torderHistory: [],\n\t\t\t\tisLoading: false,\n\t\t\t\tshowNoResults: false,\n\t\t\t\tsearchTimeout: null,\n\t\t\t\t// 高级检索相关\n\t\t\t\tshowAdvancedSearch: false,\n\t\t\t\tadvancedFilters: {\n\t\t\t\t\tfactoryName: '',\n\t\t\t\t\tstartDate: '',\n\t\t\t\t\tendDate: ''\n\t\t\t\t},\n\t\t\t\t// 短信通知设置相关\n\t\t\t\tnotificationSettings: {\n\t\t\t\t\tallowed: false,\n\t\t\t\t\tphone: '',\n\t\t\t\t\thasPhone: false\n\t\t\t\t},\n\t\t\t\tphoneInput: '',\n\t\t\t\tisUpdatingPhone: false\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\n\n\t\t\t/**\n\t\t\t * 处理搜索输入\n\t\t\t */\n\t\t\tonSearchInput() {\n\t\t\t\t// 清除之前的搜索结果\n\t\t\t\tthis.searchResults = [];\n\t\t\t\tthis.showNoResults = false;\n\n\t\t\t\t// 防抖处理\n\t\t\t\tif (this.searchTimeout) {\n\t\t\t\t\tclearTimeout(this.searchTimeout);\n\t\t\t\t}\n\n\t\t\t\tif (this.searchKeyword.trim()) {\n\t\t\t\t\tthis.searchTimeout = setTimeout(() => {\n\t\t\t\t\t\tthis.handleSearch();\n\t\t\t\t\t}, 500);\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理搜索\n\t\t\t */\n\t\t\tasync handleSearch() {\n\t\t\t\tif (!this.searchKeyword.trim()) {\n\t\t\t\t\tthis.searchResults = [];\n\t\t\t\t\tthis.showNoResults = false;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('🔍 搜索订单:', this.searchKeyword);\n\n\t\t\t\t\tconst token = followerUserManager.getToken();\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tshowError('请先登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst response = await apiService.request({\n\t\t\t\t\t\turl: `/api/orders/follower-query/${encodeURIComponent(this.searchKeyword.trim())}`,\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Authorization': `Bearer ${token}`\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\t// 处理搜索结果\n\t\t\t\t\t\tif (Array.isArray(response.data)) {\n\t\t\t\t\t\t\tthis.searchResults = response.data;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tthis.searchResults = [response.data];\n\t\t\t\t\t\t}\n\t\t\t\t\t\tthis.showNoResults = false;\n\t\t\t\t\t\tconsole.log('✅ 搜索成功，找到', this.searchResults.length, '个订单');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.searchResults = [];\n\t\t\t\t\t\tthis.showNoResults = true;\n\t\t\t\t\t\tconsole.log('⚠️ 搜索无结果:', response.message);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 搜索订单失败:', error);\n\t\t\t\t\tthis.searchResults = [];\n\t\t\t\t\tthis.showNoResults = true;\n\t\t\t\t\tshowError('搜索失败，请稍后重试');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 查看订单详情\n\t\t\t */\n\t\t\tasync viewOrderDetail(order) {\n\t\t\t\tconsole.log('📋 查看订单详情:', order.order_number);\n\n\t\t\t\ttry {\n\t\t\t\t\t// 显示加载提示\n\t\t\t\t\tuni.showLoading({\n\t\t\t\t\t\ttitle: '获取访问权限...'\n\t\t\t\t\t});\n\n\t\t\t\t\t// 保存当前跟单员用户信息\n\t\t\t\t\tconst currentFollowerInfo = followerUserManager.getUserInfo();\n\t\t\t\t\tuni.setStorageSync('follower_user_info', currentFollowerInfo);\n\n\t\t\t\t\t// 获取工厂访问token\n\t\t\t\t\tconst accessToken = await this.getFactoryAccessToken(order.factory_name);\n\n\t\t\t\t\tif (accessToken) {\n\t\t\t\t\t\tconsole.log('✅ 获取到工厂访问token:', accessToken);\n\t\t\t\t\t\t// 将访问token存储到临时存储中\n\t\t\t\t\t\tuni.setStorageSync('temp_factory_access_token', accessToken);\n\n\t\t\t\t\t\t// 清除仓库管理登录状态，确保跟单员访问结束后需要重新登录仓库管理\n\t\t\t\t\t\ttry {\n\t\t\t\t\t\t\twarehouseUserManager.logout();\n\t\t\t\t\t\t\tconsole.log('✅ 跟单员访问开始，已清除仓库管理登录状态');\n\t\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\t\tconsole.error('❌ 清除仓库管理登录状态失败:', error);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// 跳转到图片管理页面\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: `/pages/imageManage/imageManage?orderNumber=${order.order_number}&factoryName=${order.factory_name}&readonly=true&followerAccess=true`\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 获取工厂访问权限失败:', error);\n\t\t\t\t\tshowError(error.message || '获取访问权限失败');\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取工厂访问token\n\t\t\t */\n\t\t\tasync getFactoryAccessToken(factoryName) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconst requestConfig = followerUserManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl('/api/auth/follower-factory-access'),\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tfactory_name: factoryName\n\t\t\t\t\t\t},\n\t\t\t\t\t\ttimeout: 10000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200 && res.data.success) {\n\t\t\t\t\t\t\t\tresolve(res.data.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(new Error(res.data?.message || '获取访问权限失败'));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('获取工厂访问token请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.request(requestConfig);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 格式化日期\n\t\t\t */\n\t\t\tformatDate(dateString) {\n\t\t\t\tif (!dateString) return '未知时间';\n\n\t\t\t\ttry {\n\t\t\t\t\tconst date = new Date(dateString);\n\t\t\t\t\tconst year = date.getFullYear();\n\t\t\t\t\tconst month = String(date.getMonth() + 1).padStart(2, '0');\n\t\t\t\t\tconst day = String(date.getDate()).padStart(2, '0');\n\t\t\t\t\tconst hours = String(date.getHours()).padStart(2, '0');\n\t\t\t\t\tconst minutes = String(date.getMinutes()).padStart(2, '0');\n\n\t\t\t\t\treturn `${year}-${month}-${day} ${hours}:${minutes}`;\n\t\t\t\t} catch (error) {\n\t\t\t\t\treturn '时间格式错误';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 加载订单历史\n\t\t\t */\n\t\t\tasync loadOrderHistory() {\n\t\t\t\ttry {\n\t\t\t\t\tthis.isLoading = true;\n\t\t\t\t\tconsole.log('📋 加载跟单员订单历史...');\n\n\t\t\t\t\tconst token = followerUserManager.getToken();\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tshowError('请先登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst response = await apiService.request({\n\t\t\t\t\t\turl: '/api/orders/follower-history',\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Authorization': `Bearer ${token}`\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.orderHistory = response.data || [];\n\t\t\t\t\t\tconsole.log('✅ 订单历史加载成功，共', this.orderHistory.length, '个订单');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('❌ 加载订单历史失败:', response.message);\n\t\t\t\t\t\tshowError('加载订单历史失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 加载订单历史异常:', error);\n\t\t\t\t\tshowError('加载订单历史失败，请稍后重试');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 处理登出\n\t\t\t */\n\t\t\thandleLogout() {\n\t\t\t\tuni.showModal({\n\t\t\t\t\ttitle: '确认登出',\n\t\t\t\t\tcontent: '您确定要退出登录吗？',\n\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\tif (res.confirm) {\n\t\t\t\t\t\t\t// 执行登出\n\t\t\t\t\t\t\tconst logoutSuccess = followerUserManager.logout();\n\n\t\t\t\t\t\t\tif (logoutSuccess) {\n\t\t\t\t\t\t\t\tshowSuccess('已退出登录');\n\n\t\t\t\t\t\t\t\t// 跳转到首页\n\t\t\t\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\t\t\t\t\turl: '/pages/home/<USER>'\n\t\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t\t}, 1500);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\tshowError('退出登录失败');\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 初始化用户信息\n\t\t\t */\n\t\t\tinitUserInfo() {\n\t\t\t\t// 获取当前登录的跟单员信息\n\t\t\t\tthis.currentUser = followerUserManager.getUserInfo();\n\n\t\t\t\tif (!this.currentUser) {\n\t\t\t\t\t// 未登录，跳转到登录页面\n\t\t\t\t\tuni.reLaunch({\n\t\t\t\t\t\turl: '/pages/followerLogin/followerLogin'\n\t\t\t\t\t});\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tconsole.log('✅ 跟单员信息加载成功:', this.currentUser);\n\n\t\t\t\t// 加载订单历史\n\t\t\t\tthis.loadOrderHistory();\n\n\t\t\t\t// 加载短信通知设置\n\t\t\t\tthis.loadNotificationSettings();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 加载短信通知设置\n\t\t\t */\n\t\t\tasync loadNotificationSettings() {\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('📱 加载短信通知设置...');\n\n\t\t\t\t\tconst token = followerUserManager.getToken();\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tconsole.error('❌ 未找到token');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst response = await apiService.request({\n\t\t\t\t\t\turl: '/api/sms/notification-settings',\n\t\t\t\t\t\tmethod: 'GET',\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Authorization': `Bearer ${token}`\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.notificationSettings = response.data;\n\t\t\t\t\t\tthis.phoneInput = response.data.phone || '';\n\t\t\t\t\t\tconsole.log('✅ 短信通知设置加载成功:', response.data);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('❌ 加载短信通知设置失败:', response.message);\n\t\t\t\t\t\tshowError('加载短信通知设置失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 加载短信通知设置异常:', error);\n\t\t\t\t\tshowError('加载短信通知设置失败，请稍后重试');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 短信通知开关切换\n\t\t\t */\n\t\t\tasync onNotificationToggle(event) {\n\t\t\t\tconst newValue = event.detail.value;\n\n\t\t\t\tif (!this.notificationSettings.hasPhone) {\n\t\t\t\t\tshowError('请先添加手机号码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tconsole.log('📱 切换短信通知开关:', newValue);\n\n\t\t\t\t\tconst token = followerUserManager.getToken();\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tshowError('请先登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst response = await apiService.request({\n\t\t\t\t\t\turl: '/api/sms/notification-settings',\n\t\t\t\t\t\tmethod: 'PUT',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tallowed: newValue\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Authorization': `Bearer ${token}`\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.notificationSettings.allowed = newValue;\n\t\t\t\t\t\tshowSuccess(newValue ? '短信通知已开启' : '短信通知已关闭');\n\t\t\t\t\t\tconsole.log('✅ 短信通知开关更新成功');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('❌ 更新短信通知开关失败:', response.message);\n\t\t\t\t\t\tshowError('更新失败：' + response.message);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 更新短信通知开关异常:', error);\n\t\t\t\t\tshowError('更新失败，请稍后重试');\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 更新手机号码\n\t\t\t */\n\t\t\tasync updatePhone() {\n\t\t\t\tconst phone = this.phoneInput.trim();\n\n\t\t\t\tif (!phone) {\n\t\t\t\t\tshowError('请输入手机号码');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\t// 验证手机号格式\n\t\t\t\tconst phoneRegex = /^1[3-9]\\d{9}$/;\n\t\t\t\tif (!phoneRegex.test(phone)) {\n\t\t\t\t\tshowError('手机号格式不正确');\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\ttry {\n\t\t\t\t\tthis.isUpdatingPhone = true;\n\t\t\t\t\tconsole.log('📱 更新手机号码:', phone);\n\n\t\t\t\t\tconst token = followerUserManager.getToken();\n\t\t\t\t\tif (!token) {\n\t\t\t\t\t\tshowError('请先登录');\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\n\t\t\t\t\tconst response = await apiService.request({\n\t\t\t\t\t\turl: '/api/sms/notification-settings',\n\t\t\t\t\t\tmethod: 'PUT',\n\t\t\t\t\t\tdata: {\n\t\t\t\t\t\t\tphone: phone\n\t\t\t\t\t\t},\n\t\t\t\t\t\theader: {\n\t\t\t\t\t\t\t'Authorization': `Bearer ${token}`\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.notificationSettings.phone = phone;\n\t\t\t\t\t\tthis.notificationSettings.hasPhone = true;\n\t\t\t\t\t\tshowSuccess('手机号码更新成功');\n\t\t\t\t\t\tconsole.log('✅ 手机号码更新成功');\n\t\t\t\t\t} else {\n\t\t\t\t\t\tconsole.error('❌ 更新手机号码失败:', response.message);\n\t\t\t\t\t\tshowError('更新失败：' + response.message);\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 更新手机号码异常:', error);\n\t\t\t\t\tshowError('更新失败，请稍后重试');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isUpdatingPhone = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 切换高级检索显示状态\n\t\t\t */\n\t\t\ttoggleAdvancedSearch() {\n\t\t\t\tthis.showAdvancedSearch = !this.showAdvancedSearch;\n\t\t\t\tconsole.log('🔽 切换高级检索:', this.showAdvancedSearch);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 高级筛选条件变化\n\t\t\t */\n\t\t\tonAdvancedFilterChange() {\n\t\t\t\t// 可以在这里添加实时筛选逻辑\n\t\t\t\tconsole.log('🔍 高级筛选条件变化:', this.advancedFilters);\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 清除坯布商筛选\n\t\t\t */\n\t\t\tclearFactoryFilter() {\n\t\t\t\tthis.advancedFilters.factoryName = '';\n\t\t\t\tthis.onAdvancedFilterChange();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 开始日期变化\n\t\t\t */\n\t\t\tonStartDateChange(e) {\n\t\t\t\tthis.advancedFilters.startDate = e.detail.value;\n\t\t\t\tconsole.log('📅 开始日期变化:', this.advancedFilters.startDate);\n\t\t\t\tthis.onAdvancedFilterChange();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 结束日期变化\n\t\t\t */\n\t\t\tonEndDateChange(e) {\n\t\t\t\tthis.advancedFilters.endDate = e.detail.value;\n\t\t\t\tconsole.log('📅 结束日期变化:', this.advancedFilters.endDate);\n\t\t\t\tthis.onAdvancedFilterChange();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 重置高级筛选条件\n\t\t\t */\n\t\t\tresetAdvancedFilters() {\n\t\t\t\tthis.advancedFilters = {\n\t\t\t\t\tfactoryName: '',\n\t\t\t\t\tstartDate: '',\n\t\t\t\t\tendDate: ''\n\t\t\t\t};\n\t\t\t\tconsole.log('🔄 重置高级筛选条件');\n\t\t\t\t// 重置后可以选择是否立即执行搜索\n\t\t\t\tthis.handleAdvancedSearch();\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 清空检索结果\n\t\t\t */\n\t\t\tclearSearchResults() {\n\t\t\t\tthis.searchResults = [];\n\t\t\t\tthis.showNoResults = false;\n\t\t\t\tthis.searchKeyword = '';\n\t\t\t\tthis.advancedFilters = {\n\t\t\t\t\tfactoryName: '',\n\t\t\t\t\tstartDate: '',\n\t\t\t\t\tendDate: ''\n\t\t\t\t};\n\t\t\t\tconsole.log('🗑️ 清空检索结果');\n\t\t\t\tshowSuccess('已清空检索结果');\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 执行高级检索\n\t\t\t */\n\t\t\tasync handleAdvancedSearch() {\n\t\t\t\tconsole.log('🔍 执行高级检索:', this.advancedFilters);\n\n\t\t\t\ttry {\n\t\t\t\t\tthis.isLoading = true;\n\t\t\t\t\tthis.searchResults = [];\n\t\t\t\t\tthis.showNoResults = false;\n\n\t\t\t\t\t// 构建查询参数\n\t\t\t\t\tconst queryParams = {};\n\n\t\t\t\t\t// 添加基础搜索关键词\n\t\t\t\t\tif (this.searchKeyword.trim()) {\n\t\t\t\t\t\tqueryParams.orderNumber = this.searchKeyword.trim();\n\t\t\t\t\t}\n\n\t\t\t\t\t// 添加坯布商筛选\n\t\t\t\t\tif (this.advancedFilters.factoryName.trim()) {\n\t\t\t\t\t\tqueryParams.factoryName = this.advancedFilters.factoryName.trim();\n\t\t\t\t\t}\n\n\t\t\t\t\t// 添加日期范围筛选\n\t\t\t\t\tif (this.advancedFilters.startDate) {\n\t\t\t\t\t\tqueryParams.startDate = this.advancedFilters.startDate;\n\t\t\t\t\t}\n\t\t\t\t\tif (this.advancedFilters.endDate) {\n\t\t\t\t\t\tqueryParams.endDate = this.advancedFilters.endDate;\n\t\t\t\t\t}\n\n\t\t\t\t\tconsole.log('📋 高级检索参数:', queryParams);\n\n\t\t\t\t\t// 调用后端API进行高级检索\n\t\t\t\t\tconst response = await this.advancedSearchRequest(queryParams);\n\n\t\t\t\t\tif (response.success) {\n\t\t\t\t\t\tthis.searchResults = response.data || [];\n\t\t\t\t\t\tthis.showNoResults = this.searchResults.length === 0;\n\n\t\t\t\t\t\tif (this.searchResults.length > 0) {\n\t\t\t\t\t\t\tshowSuccess(`找到 ${this.searchResults.length} 条相关订单`);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tshowError('未找到符合条件的订单');\n\t\t\t\t\t\t}\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthrow new Error(response.message || '检索失败');\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('❌ 高级检索失败:', error);\n\t\t\t\t\tthis.searchResults = [];\n\t\t\t\t\tthis.showNoResults = true;\n\t\t\t\t\tshowError('检索失败，请稍后重试');\n\t\t\t\t} finally {\n\t\t\t\t\tthis.isLoading = false;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 高级检索请求\n\t\t\t */\n\t\t\tasync advancedSearchRequest(queryParams) {\n\t\t\t\treturn new Promise((resolve, reject) => {\n\t\t\t\t\tconst requestConfig = followerUserManager.createAuthRequest({\n\t\t\t\t\t\turl: urlConfig.getApiUrl('/api/orders/follower-advanced-search'),\n\t\t\t\t\t\tmethod: 'POST',\n\t\t\t\t\t\tdata: queryParams,\n\t\t\t\t\t\ttimeout: 10000,\n\t\t\t\t\t\tsuccess: (res) => {\n\t\t\t\t\t\t\tif (res.statusCode === 200) {\n\t\t\t\t\t\t\t\tresolve(res.data);\n\t\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t\treject(new Error(`服务器错误 (${res.statusCode})`));\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t},\n\t\t\t\t\t\tfail: (err) => {\n\t\t\t\t\t\t\tconsole.error('高级检索请求失败:', err);\n\t\t\t\t\t\t\treject(new Error('网络连接失败'));\n\t\t\t\t\t\t}\n\t\t\t\t\t});\n\n\t\t\t\t\tuni.request(requestConfig);\n\t\t\t\t});\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 获取发货状态样式类名\n\t\t\t */\n\t\t\tgetShippingStatusClass(status) {\n\t\t\t\tswitch (status) {\n\t\t\t\t\tcase 'shipped':\n\t\t\t\t\tcase '已发货':\n\t\t\t\t\t\treturn 'status-shipped';\n\t\t\t\t\tcase 'overdue':\n\t\t\t\t\tcase '超时':\n\t\t\t\t\t\treturn 'status-overdue';\n\t\t\t\t\tcase 'pending':\n\t\t\t\t\tcase '未发货':\n\t\t\t\t\t\treturn 'status-pending';\n\t\t\t\t\tcase '通知发货':\n\t\t\t\t\t\treturn 'status-notified';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn 'status-unknown';\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t/**\n\t\t\t * 根据position获取用户身份文本\n\t\t\t */\n\t\t\tgetUserPositionText(position) {\n\t\t\t\tswitch (position) {\n\t\t\t\t\tcase 1:\n\t\t\t\t\t\treturn '跟单员';\n\t\t\t\t\tcase 2:\n\t\t\t\t\tcase 3:\n\t\t\t\t\t\treturn '订坯人员';\n\t\t\t\t\tdefault:\n\t\t\t\t\t\treturn '跟单员';\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\n\t\tonLoad() {\n\t\t\t// 检查登录状态\n\t\t\tif (!followerUserManager.checkLoginStatus()) {\n\t\t\t\t// 未登录，跳转到登录页面\n\t\t\t\tuni.reLaunch({\n\t\t\t\t\turl: '/pages/followerLogin/followerLogin'\n\t\t\t\t});\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// 初始化用户信息\n\t\t\tthis.initUserInfo();\n\t\t},\n\n\t\tonShow() {\n\t\t\t// 页面显示时刷新用户信息\n\t\t\tif (followerUserManager.checkLoginStatus()) {\n\t\t\t\tthis.initUserInfo();\n\t\t\t}\n\t\t}\n\t}\n</script>\n\n<style scoped>\n\t.container {\n\t\tmin-height: 100vh;\n\t\tbackground: #f5f7fa;\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t}\n\n\t/* 头部样式 */\n\t.header {\n\t\tbackground: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n\t\tpadding: 40rpx 30rpx 30rpx;\n\t\tcolor: white;\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(102, 126, 234, 0.3);\n\t}\n\n\t.title-section {\n\t\tflex: 1;\n\t}\n\n\t.title {\n\t\tfont-size: 44rpx;\n\t\tfont-weight: bold;\n\t\tmargin-bottom: 8rpx;\n\t\tdisplay: block;\n\t}\n\n\t.subtitle {\n\t\tfont-size: 26rpx;\n\t\topacity: 0.9;\n\t\tdisplay: block;\n\t}\n\n\t.user-section {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 20rpx;\n\t}\n\n\t.user-info {\n\t\ttext-align: right;\n\t}\n\n\t.user-name {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tdisplay: block;\n\t\tmargin-bottom: 4rpx;\n\t}\n\n\t.user-dept {\n\t\tfont-size: 24rpx;\n\t\topacity: 0.8;\n\t\tdisplay: block;\n\t}\n\n\t.logout-btn {\n\t\twidth: 70rpx;\n\t\theight: 70rpx;\n\t\tbackground: rgba(255, 255, 255, 0.2);\n\t\tborder: none;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tbackdrop-filter: blur(10rpx);\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.logout-btn:active {\n\t\tbackground: rgba(255, 255, 255, 0.3);\n\t\ttransform: scale(0.95);\n\t}\n\n\t.logout-icon {\n\t\tfont-size: 32rpx;\n\t\tcolor: white;\n\t}\n\n\t/* 主要内容区域 */\n\t.main-content {\n\t\tflex: 1;\n\t\tpadding: 0;\n\t}\n\n\t/* 短信通知设置区域 */\n\t.notification-section {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tmargin: 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\tborder: 1rpx solid #f0f0f0;\n\t}\n\n\t.notification-card {\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.card-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 2rpx solid #f0f0f0;\n\t}\n\n\t.card-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.card-content {\n\t\tpadding-top: 20rpx;\n\t}\n\n\t.phone-section {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.phone-label {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tmargin-bottom: 15rpx;\n\t}\n\n\t.label-text {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t}\n\n\t.required-mark {\n\t\tcolor: #ff4757;\n\t\tmargin-left: 5rpx;\n\t\tfont-size: 28rpx;\n\t}\n\n\t.phone-input-group {\n\t\tdisplay: flex;\n\t\tgap: 20rpx;\n\t\talign-items: center;\n\t}\n\n\t.phone-input {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tpadding: 0 20rpx;\n\t\tborder: 2rpx solid #e0e0e0;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tbackground: #fff;\n\t}\n\n\t.phone-input:focus {\n\t\tborder-color: #667eea;\n\t}\n\n\t.phone-btn {\n\t\theight: 80rpx;\n\t\tpadding: 0 30rpx;\n\t\tbackground: #667eea;\n\t\tcolor: white;\n\t\tborder: none;\n\t\tborder-radius: 10rpx;\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t}\n\n\t.phone-btn:disabled {\n\t\tbackground: #ccc;\n\t}\n\n\t.notification-tip {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10rpx;\n\t\tpadding: 15rpx 20rpx;\n\t\tbackground: #fff3cd;\n\t\tborder: 2rpx solid #ffeaa7;\n\t\tborder-radius: 10rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.tip-icon {\n\t\tfont-size: 28rpx;\n\t}\n\n\t.tip-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #856404;\n\t}\n\n\t.notification-status {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10rpx;\n\t\tpadding: 15rpx 20rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 10rpx;\n\t\tmargin-top: 20rpx;\n\t}\n\n\t.status-icon {\n\t\tfont-size: 28rpx;\n\t}\n\n\t.status-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t}\n\n\t/* 搜索区域样式 */\n\t.search-section {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tmargin: 30rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\tborder: 1rpx solid #f0f0f0;\n\t}\n\n\t/* 历史订单区域样式 */\n\t.history-section {\n\t\tmargin: 0 30rpx;\n\t}\n\n\t.section-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15rpx;\n\t\tmargin-bottom: 25rpx;\n\t\tpadding: 0 10rpx;\n\t}\n\n\t.title-icon {\n\t\tfont-size: 40rpx;\n\t}\n\n\t.title-text {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t.search-container {\n\t\tmargin-bottom: 30rpx;\n\t}\n\n\t.search-input-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 50rpx;\n\t\tpadding: 0 20rpx;\n\t\tborder: 2rpx solid #e9ecef;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.search-input-wrapper:focus-within {\n\t\tborder-color: #667eea;\n\t\tbackground: white;\n\t\tbox-shadow: 0 0 0 6rpx rgba(102, 126, 234, 0.1);\n\t}\n\n\t.search-input {\n\t\tflex: 1;\n\t\theight: 80rpx;\n\t\tfont-size: 28rpx;\n\t\tcolor: #333;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t\toutline: none;\n\t}\n\n\t.search-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tbackground: linear-gradient(135deg, #667eea, #764ba2);\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.search-btn:active {\n\t\ttransform: scale(0.95);\n\t}\n\n\t.search-icon {\n\t\tfont-size: 28rpx;\n\t\tcolor: white;\n\t}\n\n\t/* 高级检索切换按钮 */\n\t.advanced-toggle-btn {\n\t\twidth: 60rpx;\n\t\theight: 60rpx;\n\t\tbackground: #6c757d;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-left: 10rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.advanced-toggle-btn:active {\n\t\ttransform: scale(0.95);\n\t\tbackground: #5a6268;\n\t}\n\n\t.toggle-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: white;\n\t}\n\n\t/* 高级检索框 */\n\t.advanced-search {\n\t\tmargin-top: 30rpx;\n\t\tpadding: 30rpx;\n\t\tbackground: #f8f9fa;\n\t\tborder-radius: 16rpx;\n\t\tborder: 1rpx solid #e9ecef;\n\t\tanimation: slideDown 0.3s ease;\n\t}\n\n\t@keyframes slideDown {\n\t\tfrom {\n\t\t\topacity: 0;\n\t\t\ttransform: translateY(-20rpx);\n\t\t}\n\t\tto {\n\t\t\topacity: 1;\n\t\t\ttransform: translateY(0);\n\t\t}\n\t}\n\n\t.advanced-title {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 10rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tpadding-bottom: 15rpx;\n\t\tborder-bottom: 1rpx solid #dee2e6;\n\t}\n\n\t.advanced-icon {\n\t\tfont-size: 28rpx;\n\t\tcolor: #667eea;\n\t}\n\n\t.advanced-text {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #495057;\n\t}\n\n\t/* 筛选项 */\n\t.filter-item {\n\t\tmargin-bottom: 25rpx;\n\t}\n\n\t.filter-label {\n\t\tdisplay: block;\n\t\tfont-size: 26rpx;\n\t\tcolor: #495057;\n\t\tmargin-bottom: 10rpx;\n\t\tfont-weight: 500;\n\t}\n\n\t.filter-input-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tbackground: white;\n\t\tborder-radius: 12rpx;\n\t\tborder: 1rpx solid #ced4da;\n\t\tpadding: 0 20rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.filter-input-wrapper:focus-within {\n\t\tborder-color: #667eea;\n\t\tbox-shadow: 0 0 0 4rpx rgba(102, 126, 234, 0.1);\n\t}\n\n\t.filter-input {\n\t\tflex: 1;\n\t\theight: 70rpx;\n\t\tfont-size: 26rpx;\n\t\tcolor: #495057;\n\t\tbackground: transparent;\n\t\tborder: none;\n\t\toutline: none;\n\t}\n\n\t.clear-btn {\n\t\twidth: 40rpx;\n\t\theight: 40rpx;\n\t\tbackground: #dc3545;\n\t\tborder-radius: 50%;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tmargin-left: 10rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.clear-btn:active {\n\t\ttransform: scale(0.9);\n\t\tbackground: #c82333;\n\t}\n\n\t.clear-icon {\n\t\tfont-size: 20rpx;\n\t\tcolor: white;\n\t}\n\n\t/* 日期范围 */\n\t.date-range-wrapper {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 15rpx;\n\t}\n\n\t.date-input-wrapper {\n\t\tflex: 1;\n\t}\n\n\t.date-input {\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: space-between;\n\t\tbackground: white;\n\t\tborder-radius: 12rpx;\n\t\tborder: 1rpx solid #ced4da;\n\t\tpadding: 20rpx;\n\t\theight: 70rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.date-input:active {\n\t\tborder-color: #667eea;\n\t\tbackground: #f8f9ff;\n\t}\n\n\t.date-text {\n\t\tfont-size: 26rpx;\n\t\tcolor: #495057;\n\t}\n\n\t.date-icon {\n\t\tfont-size: 24rpx;\n\t\tcolor: #6c757d;\n\t}\n\n\t.date-separator {\n\t\tfont-size: 24rpx;\n\t\tcolor: #6c757d;\n\t\tfont-weight: 500;\n\t}\n\n\t/* 高级检索操作按钮 */\n\t.advanced-actions {\n\t\tdisplay: flex;\n\t\tgap: 15rpx;\n\t\tmargin-top: 30rpx;\n\t\tpadding-top: 20rpx;\n\t\tborder-top: 1rpx solid #dee2e6;\n\t}\n\n\t.action-btn {\n\t\tflex: 1;\n\t\theight: 70rpx;\n\t\tborder-radius: 12rpx;\n\t\tborder: none;\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tjustify-content: center;\n\t\tgap: 6rpx;\n\t\tfont-size: 24rpx;\n\t\tfont-weight: 500;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.reset-btn {\n\t\tbackground: #6c757d;\n\t\tcolor: white;\n\t}\n\n\t.reset-btn:active {\n\t\tbackground: #5a6268;\n\t\ttransform: scale(0.98);\n\t}\n\n\t.clear-btn {\n\t\tbackground: #dc3545;\n\t\tcolor: white;\n\t}\n\n\t.clear-btn:active {\n\t\tbackground: #c82333;\n\t\ttransform: scale(0.98);\n\t}\n\n\t.search-btn-advanced {\n\t\tbackground: #667eea;\n\t\tcolor: white;\n\t}\n\n\t.search-btn-advanced:active {\n\t\tbackground: #5a67d8;\n\t\ttransform: scale(0.98);\n\t}\n\n\t.btn-icon {\n\t\tfont-size: 24rpx;\n\t}\n\n\t.btn-text {\n\t\tfont-size: 26rpx;\n\t}\n\n\t/* 欢迎卡片 */\n\t.welcome-card {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 30rpx;\n\t}\n\n\t.welcome-icon {\n\t\tfont-size: 80rpx;\n\t}\n\n\t.welcome-text {\n\t\tflex: 1;\n\t}\n\n\t.welcome-title {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 10rpx;\n\t}\n\n\t.welcome-desc {\n\t\tfont-size: 28rpx;\n\t\tcolor: #666;\n\t\tdisplay: block;\n\t}\n\n\t/* 功能区域 */\n\t.function-area {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\tgap: 20rpx;\n\t}\n\n\t.function-card {\n\t\tbackground: white;\n\t\tborder-radius: 16rpx;\n\t\tpadding: 30rpx;\n\t\tbox-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.06);\n\t\tdisplay: flex;\n\t\talign-items: center;\n\t\tgap: 25rpx;\n\t\ttransition: all 0.3s ease;\n\t}\n\n\t.function-card:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 1rpx 6rpx rgba(0, 0, 0, 0.1);\n\t}\n\n\t.card-icon {\n\t\tfont-size: 60rpx;\n\t\twidth: 80rpx;\n\t\ttext-align: center;\n\t}\n\n\t.card-content {\n\t\tflex: 1;\n\t}\n\n\t.card-title {\n\t\tfont-size: 32rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t\tdisplay: block;\n\t\tmargin-bottom: 8rpx;\n\t}\n\n\t.card-desc {\n\t\tfont-size: 26rpx;\n\t\tcolor: #666;\n\t\tdisplay: block;\n\t}\n\n\t.card-status {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t\tbackground: #f0f0f0;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t}\n\n\t/* 订单列表样式 */\n\t.order-list {\n\t\tpadding: 0;\n\t}\n\n\t.order-item {\n\t\tbackground: white;\n\t\tborder-radius: 20rpx;\n\t\tpadding: 40rpx;\n\t\tmargin-bottom: 30rpx;\n\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);\n\t\ttransition: all 0.3s ease;\n\t\tborder: 1rpx solid #f0f0f0;\n\t}\n\n\t.order-item:active {\n\t\ttransform: scale(0.98);\n\t\tbox-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.12);\n\t}\n\n\t.order-header {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-bottom: 25rpx;\n\t\tpadding-bottom: 20rpx;\n\t\tborder-bottom: 2rpx solid #f8f9fa;\n\t}\n\n\t.order-number {\n\t\tfont-size: 36rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #2c3e50;\n\t\tletter-spacing: 1rpx;\n\t}\n\n\t.order-status-container {\n\t\tdisplay: flex;\n\t\tflex-direction: column;\n\t\talign-items: flex-end;\n\t\tgap: 8rpx;\n\t}\n\n\t.image-count {\n\t\tfont-size: 24rpx;\n\t\tcolor: #666;\n\t\tbackground: #e8f4fd;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 20rpx;\n\t\tborder: 1rpx solid #d1ecf1;\n\t}\n\n\t.shipping-status {\n\t\tfont-size: 22rpx;\n\t\tfont-weight: bold;\n\t\tpadding: 6rpx 12rpx;\n\t\tborder-radius: 16rpx;\n\t\ttext-align: center;\n\t\tmin-width: 80rpx;\n\t}\n\n\t.status-shipped {\n\t\tbackground: #d4edda;\n\t\tcolor: #155724;\n\t\tborder: 1rpx solid #c3e6cb;\n\t}\n\n\t.status-pending {\n\t\tbackground: #fff3cd;\n\t\tcolor: #856404;\n\t\tborder: 1rpx solid #ffeaa7;\n\t}\n\n\t.status-overdue {\n\t\tbackground: #f8d7da;\n\t\tcolor: #721c24;\n\t\tborder: 1rpx solid #f5c6cb;\n\t}\n\n\t.status-notified {\n\t\tbackground: #cce5ff;\n\t\tcolor: #0066cc;\n\t\tborder: 1rpx solid #99ccff;\n\t}\n\n\t.status-unknown {\n\t\tbackground: #e2e3e5;\n\t\tcolor: #383d41;\n\t\tborder: 1rpx solid #d6d8db;\n\t}\n\n\t.delay-days {\n\t\tfont-size: 20rpx;\n\t\tmargin-left: 4rpx;\n\t}\n\n\t.order-info {\n\t\tmargin-bottom: 20rpx;\n\t}\n\n\t.order-info .info-item {\n\t\tdisplay: block;\n\t\tfont-size: 30rpx;\n\t\tcolor: #555;\n\t\tline-height: 2.2;\n\t\tmargin-bottom: 8rpx;\n\t\tpadding-left: 20rpx;\n\t\tposition: relative;\n\t}\n\n\t.order-info .info-item:before {\n\t\tcontent: \"•\";\n\t\tposition: absolute;\n\t\tleft: 0;\n\t\tcolor: #667eea;\n\t\tfont-weight: bold;\n\t}\n\n\t.order-footer {\n\t\tdisplay: flex;\n\t\tjustify-content: space-between;\n\t\talign-items: center;\n\t\tmargin-top: 25rpx;\n\t\tpadding-top: 20rpx;\n\t\tborder-top: 2rpx solid #f8f9fa;\n\t}\n\n\t.order-date {\n\t\tfont-size: 28rpx;\n\t\tcolor: #7f8c8d;\n\t\tfont-weight: 500;\n\t}\n\n\t.order-company {\n\t\tfont-size: 28rpx;\n\t\tcolor: #667eea;\n\t\tfont-weight: 500;\n\t\tbackground: #f8f9ff;\n\t\tpadding: 8rpx 16rpx;\n\t\tborder-radius: 15rpx;\n\t}\n\n\t/* 搜索结果样式 */\n\t.results-header {\n\t\tpadding: 20rpx 30rpx 10rpx;\n\t}\n\n\t.results-title {\n\t\tfont-size: 28rpx;\n\t\tfont-weight: bold;\n\t\tcolor: #333;\n\t}\n\n\t/* 底部样式 */\n\t.footer {\n\t\tpadding: 30rpx;\n\t\ttext-align: center;\n\t\tbackground: white;\n\t\tborder-top: 1rpx solid #eee;\n\t}\n\n\t.footer-text {\n\t\tfont-size: 24rpx;\n\t\tcolor: #999;\n\t}\n\n\n</style>\n", "import MiniProgramPage from 'D:/Desktop/Warehouse/front/pages/followerManage/followerManage.vue'\nwx.createPage(MiniProgramPage)"], "names": ["uni", "followerUserManager", "showError", "apiService", "warehouseUserManager", "urlConfig", "showSuccess"], "mappings": ";;;;;;;AAuSC,MAAK,YAAU;AAAA,EACd,OAAO;AACN,WAAO;AAAA,MACN,aAAa;AAAA,MACb,eAAe;AAAA,MACf,eAAe,CAAE;AAAA,MACjB,cAAc,CAAE;AAAA,MAChB,WAAW;AAAA,MACX,eAAe;AAAA,MACf,eAAe;AAAA;AAAA,MAEf,oBAAoB;AAAA,MACpB,iBAAiB;AAAA,QAChB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA,MACT;AAAA;AAAA,MAED,sBAAsB;AAAA,QACrB,SAAS;AAAA,QACT,OAAO;AAAA,QACP,UAAU;AAAA,MACV;AAAA,MACD,YAAY;AAAA,MACZ,iBAAiB;AAAA,IAClB;AAAA,EACA;AAAA,EACD,SAAS;AAAA;AAAA;AAAA;AAAA,IAMR,gBAAgB;AAEf,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AAGrB,UAAI,KAAK,eAAe;AACvB,qBAAa,KAAK,aAAa;AAAA,MAChC;AAEA,UAAI,KAAK,cAAc,QAAQ;AAC9B,aAAK,gBAAgB,WAAW,MAAM;AACrC,eAAK,aAAY;AAAA,QACjB,GAAE,GAAG;AAAA,MACP;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,eAAe;AACpB,UAAI,CAAC,KAAK,cAAc,QAAQ;AAC/B,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrB;AAAA,MACD;AAEA,UAAI;AACHA,sBAAA,MAAA,MAAA,OAAA,kDAAY,YAAY,KAAK,aAAa;AAE1C,cAAM,QAAQC,8CAAoB;AAClC,YAAI,CAAC,OAAO;AACXC,wBAAS,UAAC,MAAM;AAChB;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,iBAAU,WAAC,QAAQ;AAAA,UACzC,KAAK,8BAA8B,mBAAmB,KAAK,cAAc,KAAM,CAAA,CAAC;AAAA,UAChF,QAAQ;AAAA,UACR,QAAQ;AAAA,YACP,iBAAiB,UAAU,KAAK;AAAA,UACjC;AAAA,QACD,CAAC;AAED,YAAI,SAAS,SAAS;AAErB,cAAI,MAAM,QAAQ,SAAS,IAAI,GAAG;AACjC,iBAAK,gBAAgB,SAAS;AAAA,iBACxB;AACN,iBAAK,gBAAgB,CAAC,SAAS,IAAI;AAAA,UACpC;AACA,eAAK,gBAAgB;AACrBH,6FAAY,aAAa,KAAK,cAAc,QAAQ,KAAK;AAAA,eACnD;AACN,eAAK,gBAAgB;AACrB,eAAK,gBAAgB;AACrBA,6FAAY,aAAa,SAAS,OAAO;AAAA,QAC1C;AAAA,MACC,SAAO,OAAO;AACfA,sBAAA,MAAA,MAAA,SAAA,kDAAc,aAAa,KAAK;AAChC,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrBE,sBAAS,UAAC,YAAY;AAAA,MACvB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,gBAAgB,OAAO;AAC5BF,oBAAY,MAAA,MAAA,OAAA,kDAAA,cAAc,MAAM,YAAY;AAE5C,UAAI;AAEHA,sBAAAA,MAAI,YAAY;AAAA,UACf,OAAO;AAAA,QACR,CAAC;AAGD,cAAM,sBAAsBC,8CAAoB;AAChDD,sBAAAA,MAAI,eAAe,sBAAsB,mBAAmB;AAG5D,cAAM,cAAc,MAAM,KAAK,sBAAsB,MAAM,YAAY;AAEvE,YAAI,aAAa;AAChBA,6FAAY,mBAAmB,WAAW;AAE1CA,wBAAAA,MAAI,eAAe,6BAA6B,WAAW;AAG3D,cAAI;AACHI,iCAAoB,qBAAC,OAAM;AAC3BJ,0BAAAA,MAAY,MAAA,OAAA,kDAAA,uBAAuB;AAAA,UAClC,SAAO,OAAO;AACfA,0BAAA,MAAA,MAAA,SAAA,kDAAc,mBAAmB,KAAK;AAAA,UACvC;AAGAA,wBAAAA,MAAI,WAAW;AAAA,YACd,KAAK,8CAA8C,MAAM,YAAY,gBAAgB,MAAM,YAAY;AAAA,UACxG,CAAC;AAAA,QACF;AAAA,MACC,SAAO,OAAO;AACfA,sBAAc,MAAA,MAAA,SAAA,kDAAA,iBAAiB,KAAK;AACpCE,sBAAAA,UAAU,MAAM,WAAW,UAAU;AAAA,MACtC,UAAU;AACTF,sBAAG,MAAC,YAAW;AAAA,MAChB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,sBAAsB,aAAa;AACxC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,cAAM,gBAAgBC,0BAAmB,oBAAC,kBAAkB;AAAA,UAC3D,KAAKI,gBAAAA,UAAU,UAAU,mCAAmC;AAAA,UAC5D,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,cAAc;AAAA,UACd;AAAA,UACD,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;;AACjB,gBAAI,IAAI,eAAe,OAAO,IAAI,KAAK,SAAS;AAC/C,sBAAQ,IAAI,KAAK,IAAI;AAAA,mBACf;AACN,qBAAO,IAAI,QAAM,SAAI,SAAJ,mBAAU,YAAW,UAAU,CAAC;AAAA,YAClD;AAAA,UACA;AAAA,UACD,MAAM,CAAC,QAAQ;AACdL,0BAAc,MAAA,MAAA,SAAA,kDAAA,oBAAoB,GAAG;AACrC,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACD,CAAC;AAEDA,4BAAI,QAAQ,aAAa;AAAA,MAC1B,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,WAAW,YAAY;AACtB,UAAI,CAAC;AAAY,eAAO;AAExB,UAAI;AACH,cAAM,OAAO,IAAI,KAAK,UAAU;AAChC,cAAM,OAAO,KAAK;AAClB,cAAM,QAAQ,OAAO,KAAK,SAAQ,IAAK,CAAC,EAAE,SAAS,GAAG,GAAG;AACzD,cAAM,MAAM,OAAO,KAAK,QAAS,CAAA,EAAE,SAAS,GAAG,GAAG;AAClD,cAAM,QAAQ,OAAO,KAAK,SAAU,CAAA,EAAE,SAAS,GAAG,GAAG;AACrD,cAAM,UAAU,OAAO,KAAK,WAAY,CAAA,EAAE,SAAS,GAAG,GAAG;AAEzD,eAAO,GAAG,IAAI,IAAI,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI,OAAO;AAAA,MACjD,SAAO,OAAO;AACf,eAAO;AAAA,MACR;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,mBAAmB;AACxB,UAAI;AACH,aAAK,YAAY;AACjBA,sBAAAA,qEAAY,iBAAiB;AAE7B,cAAM,QAAQC,8CAAoB;AAClC,YAAI,CAAC,OAAO;AACXC,wBAAS,UAAC,MAAM;AAChB;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,iBAAU,WAAC,QAAQ;AAAA,UACzC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,QAAQ;AAAA,YACP,iBAAiB,UAAU,KAAK;AAAA,UACjC;AAAA,QACD,CAAC;AAED,YAAI,SAAS,SAAS;AACrB,eAAK,eAAe,SAAS,QAAQ,CAAA;AACrCH,6FAAY,gBAAgB,KAAK,aAAa,QAAQ,KAAK;AAAA,eACrD;AACNA,+FAAc,eAAe,SAAS,OAAO;AAC7CE,wBAAS,UAAC,UAAU;AAAA,QACrB;AAAA,MACC,SAAO,OAAO;AACfF,sBAAA,MAAA,MAAA,SAAA,kDAAc,eAAe,KAAK;AAClCE,sBAAS,UAAC,gBAAgB;AAAA,MAC3B,UAAU;AACT,aAAK,YAAY;AAAA,MAClB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AACdF,oBAAAA,MAAI,UAAU;AAAA,QACb,OAAO;AAAA,QACP,SAAS;AAAA,QACT,SAAS,CAAC,QAAQ;AACjB,cAAI,IAAI,SAAS;AAEhB,kBAAM,gBAAgBC,8CAAoB;AAE1C,gBAAI,eAAe;AAClBK,4BAAW,YAAC,OAAO;AAGnB,yBAAW,MAAM;AAChBN,8BAAAA,MAAI,SAAS;AAAA,kBACZ,KAAK;AAAA,gBACN,CAAC;AAAA,cACD,GAAE,IAAI;AAAA,mBACD;AACNE,4BAAS,UAAC,QAAQ;AAAA,YACnB;AAAA,UACD;AAAA,QACD;AAAA,MACD,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,eAAe;AAEd,WAAK,cAAcD,8CAAoB;AAEvC,UAAI,CAAC,KAAK,aAAa;AAEtBD,sBAAAA,MAAI,SAAS;AAAA,UACZ,KAAK;AAAA,QACN,CAAC;AACD;AAAA,MACD;AAEAA,oBAAY,MAAA,MAAA,OAAA,kDAAA,gBAAgB,KAAK,WAAW;AAG5C,WAAK,iBAAgB;AAGrB,WAAK,yBAAwB;AAAA,IAC7B;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,2BAA2B;AAChC,UAAI;AACHA,sBAAAA,qEAAY,gBAAgB;AAE5B,cAAM,QAAQC,8CAAoB;AAClC,YAAI,CAAC,OAAO;AACXD,wBAAAA,MAAA,MAAA,SAAA,kDAAc,YAAY;AAC1B;AAAA,QACD;AAEA,cAAM,WAAW,MAAMG,iBAAU,WAAC,QAAQ;AAAA,UACzC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,QAAQ;AAAA,YACP,iBAAiB,UAAU,KAAK;AAAA,UACjC;AAAA,QACD,CAAC;AAED,YAAI,SAAS,SAAS;AACrB,eAAK,uBAAuB,SAAS;AACrC,eAAK,aAAa,SAAS,KAAK,SAAS;AACzCH,6FAAY,iBAAiB,SAAS,IAAI;AAAA,eACpC;AACNA,wBAAA,MAAA,MAAA,SAAA,kDAAc,iBAAiB,SAAS,OAAO;AAC/CE,wBAAS,UAAC,YAAY;AAAA,QACvB;AAAA,MACC,SAAO,OAAO;AACfF,sBAAc,MAAA,MAAA,SAAA,kDAAA,iBAAiB,KAAK;AACpCE,sBAAS,UAAC,kBAAkB;AAAA,MAC7B;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,qBAAqB,OAAO;AACjC,YAAM,WAAW,MAAM,OAAO;AAE9B,UAAI,CAAC,KAAK,qBAAqB,UAAU;AACxCA,sBAAS,UAAC,UAAU;AACpB;AAAA,MACD;AAEA,UAAI;AACHF,sBAAY,MAAA,MAAA,OAAA,kDAAA,gBAAgB,QAAQ;AAEpC,cAAM,QAAQC,8CAAoB;AAClC,YAAI,CAAC,OAAO;AACXC,wBAAS,UAAC,MAAM;AAChB;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,iBAAU,WAAC,QAAQ;AAAA,UACzC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACL,SAAS;AAAA,UACT;AAAA,UACD,QAAQ;AAAA,YACP,iBAAiB,UAAU,KAAK;AAAA,UACjC;AAAA,QACD,CAAC;AAED,YAAI,SAAS,SAAS;AACrB,eAAK,qBAAqB,UAAU;AACpCG,wBAAAA,YAAY,WAAW,YAAY,SAAS;AAC5CN,wBAAAA,MAAY,MAAA,OAAA,kDAAA,cAAc;AAAA,eACpB;AACNA,wBAAA,MAAA,MAAA,SAAA,kDAAc,iBAAiB,SAAS,OAAO;AAC/CE,wBAAAA,UAAU,UAAU,SAAS,OAAO;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfF,sBAAc,MAAA,MAAA,SAAA,kDAAA,iBAAiB,KAAK;AACpCE,sBAAS,UAAC,YAAY;AAAA,MACvB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,cAAc;AACnB,YAAM,QAAQ,KAAK,WAAW,KAAI;AAElC,UAAI,CAAC,OAAO;AACXA,sBAAS,UAAC,SAAS;AACnB;AAAA,MACD;AAGA,YAAM,aAAa;AACnB,UAAI,CAAC,WAAW,KAAK,KAAK,GAAG;AAC5BA,sBAAS,UAAC,UAAU;AACpB;AAAA,MACD;AAEA,UAAI;AACH,aAAK,kBAAkB;AACvBF,sBAAA,MAAA,MAAA,OAAA,kDAAY,cAAc,KAAK;AAE/B,cAAM,QAAQC,8CAAoB;AAClC,YAAI,CAAC,OAAO;AACXC,wBAAS,UAAC,MAAM;AAChB;AAAA,QACD;AAEA,cAAM,WAAW,MAAMC,iBAAU,WAAC,QAAQ;AAAA,UACzC,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACL;AAAA,UACA;AAAA,UACD,QAAQ;AAAA,YACP,iBAAiB,UAAU,KAAK;AAAA,UACjC;AAAA,QACD,CAAC;AAED,YAAI,SAAS,SAAS;AACrB,eAAK,qBAAqB,QAAQ;AAClC,eAAK,qBAAqB,WAAW;AACrCG,wBAAW,YAAC,UAAU;AACtBN,wBAAAA,MAAA,MAAA,OAAA,kDAAY,YAAY;AAAA,eAClB;AACNA,+FAAc,eAAe,SAAS,OAAO;AAC7CE,wBAAAA,UAAU,UAAU,SAAS,OAAO;AAAA,QACrC;AAAA,MACC,SAAO,OAAO;AACfF,sBAAA,MAAA,MAAA,SAAA,kDAAc,eAAe,KAAK;AAClCE,sBAAS,UAAC,YAAY;AAAA,MACvB,UAAU;AACT,aAAK,kBAAkB;AAAA,MACxB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,uBAAuB;AACtB,WAAK,qBAAqB,CAAC,KAAK;AAChCF,yFAAY,cAAc,KAAK,kBAAkB;AAAA,IACjD;AAAA;AAAA;AAAA;AAAA,IAKD,yBAAyB;AAExBA,yFAAY,gBAAgB,KAAK,eAAe;AAAA,IAChD;AAAA;AAAA;AAAA;AAAA,IAKD,qBAAqB;AACpB,WAAK,gBAAgB,cAAc;AACnC,WAAK,uBAAsB;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA,IAKD,kBAAkB,GAAG;AACpB,WAAK,gBAAgB,YAAY,EAAE,OAAO;AAC1CA,0BAAA,MAAA,OAAA,kDAAY,cAAc,KAAK,gBAAgB,SAAS;AACxD,WAAK,uBAAsB;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA,IAKD,gBAAgB,GAAG;AAClB,WAAK,gBAAgB,UAAU,EAAE,OAAO;AACxCA,0BAAA,MAAA,OAAA,kDAAY,cAAc,KAAK,gBAAgB,OAAO;AACtD,WAAK,uBAAsB;AAAA,IAC3B;AAAA;AAAA;AAAA;AAAA,IAKD,uBAAuB;AACtB,WAAK,kBAAkB;AAAA,QACtB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA;AAEVA,oBAAAA,qEAAY,aAAa;AAEzB,WAAK,qBAAoB;AAAA,IACzB;AAAA;AAAA;AAAA;AAAA,IAKD,qBAAqB;AACpB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,gBAAgB;AACrB,WAAK,kBAAkB;AAAA,QACtB,aAAa;AAAA,QACb,WAAW;AAAA,QACX,SAAS;AAAA;AAEVA,oBAAAA,MAAY,MAAA,OAAA,kDAAA,YAAY;AACxBM,oBAAW,YAAC,SAAS;AAAA,IACrB;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,uBAAuB;AAC5BN,oBAAY,MAAA,MAAA,OAAA,kDAAA,cAAc,KAAK,eAAe;AAE9C,UAAI;AACH,aAAK,YAAY;AACjB,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AAGrB,cAAM,cAAc,CAAA;AAGpB,YAAI,KAAK,cAAc,QAAQ;AAC9B,sBAAY,cAAc,KAAK,cAAc,KAAI;AAAA,QAClD;AAGA,YAAI,KAAK,gBAAgB,YAAY,KAAI,GAAI;AAC5C,sBAAY,cAAc,KAAK,gBAAgB,YAAY,KAAI;AAAA,QAChE;AAGA,YAAI,KAAK,gBAAgB,WAAW;AACnC,sBAAY,YAAY,KAAK,gBAAgB;AAAA,QAC9C;AACA,YAAI,KAAK,gBAAgB,SAAS;AACjC,sBAAY,UAAU,KAAK,gBAAgB;AAAA,QAC5C;AAEAA,sBAAY,MAAA,MAAA,OAAA,kDAAA,cAAc,WAAW;AAGrC,cAAM,WAAW,MAAM,KAAK,sBAAsB,WAAW;AAE7D,YAAI,SAAS,SAAS;AACrB,eAAK,gBAAgB,SAAS,QAAQ,CAAA;AACtC,eAAK,gBAAgB,KAAK,cAAc,WAAW;AAEnD,cAAI,KAAK,cAAc,SAAS,GAAG;AAClCM,0BAAW,YAAC,MAAM,KAAK,cAAc,MAAM,QAAQ;AAAA,iBAC7C;AACNJ,0BAAS,UAAC,YAAY;AAAA,UACvB;AAAA,eACM;AACN,gBAAM,IAAI,MAAM,SAAS,WAAW,MAAM;AAAA,QAC3C;AAAA,MACC,SAAO,OAAO;AACfF,sBAAA,MAAA,MAAA,SAAA,kDAAc,aAAa,KAAK;AAChC,aAAK,gBAAgB;AACrB,aAAK,gBAAgB;AACrBE,sBAAS,UAAC,YAAY;AAAA,MACvB,UAAU;AACT,aAAK,YAAY;AAAA,MAClB;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,MAAM,sBAAsB,aAAa;AACxC,aAAO,IAAI,QAAQ,CAAC,SAAS,WAAW;AACvC,cAAM,gBAAgBD,0BAAmB,oBAAC,kBAAkB;AAAA,UAC3D,KAAKI,gBAAAA,UAAU,UAAU,sCAAsC;AAAA,UAC/D,QAAQ;AAAA,UACR,MAAM;AAAA,UACN,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACjB,gBAAI,IAAI,eAAe,KAAK;AAC3B,sBAAQ,IAAI,IAAI;AAAA,mBACV;AACN,qBAAO,IAAI,MAAM,UAAU,IAAI,UAAU,GAAG,CAAC;AAAA,YAC9C;AAAA,UACA;AAAA,UACD,MAAM,CAAC,QAAQ;AACdL,0BAAc,MAAA,MAAA,SAAA,kDAAA,aAAa,GAAG;AAC9B,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC3B;AAAA,QACD,CAAC;AAEDA,4BAAI,QAAQ,aAAa;AAAA,MAC1B,CAAC;AAAA,IACD;AAAA;AAAA;AAAA;AAAA,IAKD,uBAAuB,QAAQ;AAC9B,cAAQ,QAAM;AAAA,QACb,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACA;AAAA;AAAA;AAAA;AAAA,IAKD,oBAAoB,UAAU;AAC7B,cAAQ,UAAQ;AAAA,QACf,KAAK;AACJ,iBAAO;AAAA,QACR,KAAK;AAAA,QACL,KAAK;AACJ,iBAAO;AAAA,QACR;AACC,iBAAO;AAAA,MACT;AAAA,IACD;AAAA,EACA;AAAA,EAED,SAAS;AAER,QAAI,CAACC,0BAAAA,oBAAoB,oBAAoB;AAE5CD,oBAAAA,MAAI,SAAS;AAAA,QACZ,KAAK;AAAA,MACN,CAAC;AACD;AAAA,IACD;AAGA,SAAK,aAAY;AAAA,EACjB;AAAA,EAED,SAAS;AAER,QAAIC,0BAAAA,oBAAoB,oBAAoB;AAC3C,WAAK,aAAY;AAAA,IAClB;AAAA,EACD;AACD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACh6BD,GAAG,WAAW,eAAe;"}
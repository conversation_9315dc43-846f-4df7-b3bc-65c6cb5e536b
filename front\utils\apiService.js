/**
 * API服务模块
 * 统一管理所有API调用逻辑
 */

// 根据环境自动选择API基础URL
const getBaseUrl = () => {
	// 检查是否在微信小程序环境
	// #ifdef MP-WEIXIN
	// 修改为连接本地开发服务器
	return 'https://www.mls2005.top';
	// #endif

	// 检查是否在H5环境
	// #ifdef H5
	if (window.location.protocol === 'https:') {
		return 'https://www.mls2005.top';
	}
	return 'http://localhost:3001';
	// #endif

	// 其他环境默认使用本地开发服务器
	return 'https://www.mls2005.top';
};

const API_CONFIG = {
	baseUrl: getBaseUrl(),
	timeout: 30000
};

/**
 * API服务类
 */
class ApiService {
	constructor() {
		this.baseUrl = API_CONFIG.baseUrl;
		this.timeout = API_CONFIG.timeout;
	}

	/**
	 * 通用请求方法
	 * @param {Object} options 请求选项
	 */
	async request(options) {
		const {
			url,
			method = 'GET',
			data = {},
			header = {},
			timeout = this.timeout
		} = options;

		return new Promise((resolve, reject) => {
			const requestTimeout = setTimeout(() => {
				reject(new Error('请求超时，请检查网络连接'));
			}, timeout);

			uni.request({
				url: url.startsWith('http') ? url : `${this.baseUrl}${url}`,
				method: method,
				data: data,
				header: {
					'Content-Type': 'application/json',
					...header
				},
				success: (response) => {
					clearTimeout(requestTimeout);

					if (response.statusCode >= 200 && response.statusCode < 300) {
						resolve(response.data);
					} else {
						reject(new Error(`请求失败 (${response.statusCode}): ${response.data?.message || '未知错误'}`));
					}
				},
				fail: (error) => {
					clearTimeout(requestTimeout);
					console.error('请求失败:', error);

					let errorMessage = '请求失败';
					if (error.errMsg) {
						if (error.errMsg.includes('timeout')) {
							errorMessage = '请求超时，请检查网络连接';
						} else if (error.errMsg.includes('fail')) {
							errorMessage = '网络连接失败，请检查服务器是否启动';
						} else {
							errorMessage = error.errMsg;
						}
					}

					reject(new Error(errorMessage));
				}
			});
		});
	}

	/**
	 * 检查服务器连接
	 */
	async checkServerConnection() {
		const maxRetries = 3;
		const retryDelay = 2000; // 2秒

		for (let attempt = 1; attempt <= maxRetries; attempt++) {
			try {
				console.log(`检查服务器连接 (尝试 ${attempt}/${maxRetries}):`, `${this.baseUrl}/api/health`);
				const response = await uni.request({
					url: `${this.baseUrl}/api/health`,
					method: 'GET',
					timeout: 15000 // 增加到15秒
				});
				console.log('服务器连接检查结果:', response);
				if (response.statusCode === 200) {
					console.log('✅ 服务器连接正常');
					return true;
				} else {
					console.log('❌ 服务器连接失败，状态码:', response.statusCode);
					if (attempt === maxRetries) return false;
				}
			} catch (error) {
				console.error(`❌ 服务器连接检查失败 (尝试 ${attempt}/${maxRetries}):`, error);

				// 详细错误诊断
				if (error.errno) {
					console.error('错误代码:', error.errno);
					if (error.errno === 600001) {
						console.error('网络连接被拒绝 - 可能原因:');
						console.error('1. 服务器未启动');
						console.error('2. 端口被防火墙阻止');
						console.error('3. 服务器地址或端口错误');
					} else if (error.errno === 5) {
						console.error('请求超时 - 可能原因:');
						console.error('1. 网络连接缓慢');
						console.error('2. 服务器响应缓慢');
						console.error('3. 防火墙阻止连接');
					}
				}

				if (attempt === maxRetries) return false;

				// 等待后重试
				if (attempt < maxRetries) {
					console.log(`等待 ${retryDelay/1000} 秒后重试...`);
					await new Promise(resolve => setTimeout(resolve, retryDelay));
				}
			}
		}
		return false;
	}

	/**
	 * 通用图片识别
	 * @param {string} imagePath 图片路径
	 * @param {string} username 用户名（可选）
	 */
	async recognizeImage(imagePath, username = null) {
		return new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error('请求超时，请检查网络连接'));
			}, this.timeout);

			// 构建表单数据
			const formData = {};
			if (username) {
				formData.username = username;
			}

			uni.uploadFile({
				url: `${this.baseUrl}/api/recognize`,
				filePath: imagePath,
				name: 'image',
				formData: formData,
				success: (uploadRes) => {
					clearTimeout(timeout);
					try {
						if (uploadRes.statusCode !== 200) {
							reject(new Error(`服务器错误 (${uploadRes.statusCode})`));
							return;
						}

						const result = JSON.parse(uploadRes.data);
						if (result.success) {
							resolve(result);
						} else {
							reject(new Error(result.message || '识别失败'));
						}
					} catch (parseError) {
						console.error('解析响应失败:', parseError, uploadRes.data);
						reject(new Error('服务器响应格式错误'));
					}
				},
				fail: (error) => {
					clearTimeout(timeout);
					console.error('上传失败:', error);

					let errorMessage = '上传失败';
					if (error.errMsg) {
						if (error.errMsg.includes('timeout')) {
							errorMessage = '请求超时，请检查网络连接';
						} else if (error.errMsg.includes('fail')) {
							errorMessage = '网络连接失败，请检查服务器是否启动';
						} else if (error.errMsg.includes('boundary')) {
							errorMessage = '文件上传格式错误，请重试';
						} else {
							errorMessage = error.errMsg;
						}
					}

					reject(new Error(errorMessage));
				}
			});
		});
	}

	/**
	 * 批量图片识别（通用文字识别）
	 * @param {Array} imagePaths 图片路径数组
	 * @param {Function} progressCallback 进度回调函数
	 */
	async recognizeMultipleImages(imagePaths, progressCallback) {
		const results = [];

		for (let i = 0; i < imagePaths.length; i++) {
			try {
				// 调用进度回调
				if (progressCallback) {
					progressCallback(i + 1, imagePaths.length);
				}

				const result = await this.recognizeImage(imagePaths[i]);
				results.push({
					index: i,
					imagePath: imagePaths[i],
					success: true,
					result: result
				});
			} catch (error) {
				results.push({
					index: i,
					imagePath: imagePaths[i],
					success: false,
					error: error.message
				});
			}
		}

		return results;
	}

	/**
	 * 装柜放船样登记表识别
	 * @param {string} imagePath 图片路径
	 */
	async recognizeRegistrationForm(imagePath) {
		return new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error('装柜放船样登记表识别超时'));
			}, this.timeout);

			uni.uploadFile({
				url: `${this.baseUrl}/api/registration-form/recognize`,
				filePath: imagePath,
				name: 'image',
				formData: {},
				success: (uploadRes) => {
					clearTimeout(timeout);
					try {
						if (uploadRes.statusCode !== 200) {
							resolve({ isRegistrationForm: false });
							return;
						}

						const result = JSON.parse(uploadRes.data);
						if (result.success && result.isRegistrationForm) {
							resolve(result);
						} else {
							resolve({ isRegistrationForm: false });
						}
					} catch (parseError) {
						console.error('解析装柜放船样登记表响应失败:', parseError);
						resolve({ isRegistrationForm: false });
					}
				},
				fail: (error) => {
					clearTimeout(timeout);
					console.log('装柜放船样登记表识别失败，使用普通识别:', error);
					resolve({ isRegistrationForm: false });
				}
			});
		});
	}

	/**
	 * 装柜信息表识别
	 * @param {string} imagePath 图片路径
	 */
	async recognizeCartonInfo(imagePath) {
		return new Promise((resolve, reject) => {
			const timeout = setTimeout(() => {
				reject(new Error('装柜信息表识别超时'));
			}, this.timeout);

			uni.uploadFile({
				url: `${this.baseUrl}/api/carton-info/recognize`,
				filePath: imagePath,
				name: 'image',
				formData: {},
				success: (uploadRes) => {
					clearTimeout(timeout);
					try {
						if (uploadRes.statusCode !== 200) {
							resolve({ isCartonInfo: false });
							return;
						}

						const result = JSON.parse(uploadRes.data);
						if (result.success && result.isCartonInfo) {
							resolve(result);
						} else {
							resolve({ isCartonInfo: false });
						}
					} catch (parseError) {
						console.error('解析装柜信息表响应失败:', parseError);
						resolve({ isCartonInfo: false });
					}
				},
				fail: (error) => {
					clearTimeout(timeout);
					console.log('装柜信息表识别失败，使用普通识别:', error);
					resolve({ isCartonInfo: false });
				}
			});
		});
	}

	/**
	 * 保存装柜放船样登记表数据
	 * @param {Array} data 表格数据
	 * @param {Object} imageInfo 图片信息
	 */
	async saveRegistrationFormData(data, imageInfo) {
		const dataWithUploadTime = data.map(row => ({
			...row,
			UploadDate: new Date().toISOString()
		}));

		const response = await uni.request({
			url: `${this.baseUrl}/api/registration-form/save`,
			method: 'POST',
			data: {
				data: dataWithUploadTime,
				imageInfo: imageInfo,
				uploadTime: new Date().toISOString()
			},
			header: {
				'Content-Type': 'application/json'
			}
		});

		if (response.statusCode === 200 && response.data.success) {
			return response.data;
		} else {
			throw new Error(response.data.message || '保存失败');
		}
	}

	/**
	 * 保存装柜信息表数据
	 * @param {Object} data 装柜信息数据
	 * @param {Object} imageInfo 图片信息
	 */
	async saveCartonInfoData(data, imageInfo) {
		const dataWithUploadTime = {
			...data,
			UploadDate: new Date().toISOString()
		};

		const response = await uni.request({
			url: `${this.baseUrl}/api/carton-info/save`,
			method: 'POST',
			data: {
				data: dataWithUploadTime,
				imageInfo: imageInfo,
				uploadTime: new Date().toISOString()
			},
			header: {
				'Content-Type': 'application/json'
			}
		});

		if (response.statusCode === 200 && response.data.success) {
			return response.data;
		} else {
			throw new Error(response.data.message || '保存失败');
		}
	}

	/**
	 * 获取今日历史记录
	 * @param {string} username - 用户名（可选）
	 */
	async getTodayHistory(username = null) {
		try {
			console.log('📋 请求今日历史记录...');

			// 构建URL，如果有用户名则添加查询参数
			let url = `${this.baseUrl}/api/history/today`;
			if (username) {
				url += `?username=${encodeURIComponent(username)}`;
				console.log(`📋 查询用户 ${username} 的历史记录`);
			}

			const response = await uni.request({
				url: url,
				method: 'GET',
				header: {
					'Content-Type': 'application/json'
				},
				timeout: this.timeout
			});

			console.log('📋 历史记录响应:', response);

			if (response.statusCode === 200 && response.data.success) {
				return response.data;
			} else {
				throw new Error(response.data.message || '获取历史记录失败');
			}
		} catch (error) {
			console.error('❌ 获取今日历史记录失败:', error);
			throw new Error('获取历史记录失败: ' + error.message);
		}
	}

	/**
	 * 通用GET请求方法
	 * @param {string} endpoint - API端点（相对路径）
	 * @param {Object} options - 请求选项
	 * @returns {Promise} 请求结果
	 */
	async get(endpoint, options = {}) {
		try {
			const url = `${this.baseUrl}/api${endpoint}`;
			console.log(`🔍 GET请求:`, url);

			const response = await uni.request({
				url: url,
				method: 'GET',
				header: {
					'Content-Type': 'application/json',
					'Authorization': uni.getStorageSync('token') ? `Bearer ${uni.getStorageSync('token')}` : '',
					...options.headers
				},
				timeout: options.timeout || this.timeout
			});

			console.log(`✅ GET响应:`, response);

			if (response.statusCode === 200 && response.data.success) {
				return response.data;
			} else {
				throw new Error(response.data.message || `请求失败 (${response.statusCode})`);
			}
		} catch (error) {
			console.error(`❌ GET请求失败 (${endpoint}):`, error);
			throw new Error('请求失败: ' + error.message);
		}
	}

	/**
	 * 通用POST请求方法
	 * @param {string} endpoint - API端点（相对路径）
	 * @param {Object} data - 请求数据
	 * @param {Object} options - 请求选项
	 * @returns {Promise} 请求结果
	 */
	async post(endpoint, data = {}, options = {}) {
		try {
			const url = `${this.baseUrl}/api${endpoint}`;
			console.log(`📤 POST请求:`, url, data);

			// 获取token - 从userManager获取
			let token = null;
			try {
				// 尝试从全局获取userManager实例
				if (typeof getApp === 'function') {
					const app = getApp();
					if (app.globalData && app.globalData.userManager) {
						token = app.globalData.userManager.getToken();
					}
				}

				// 如果没有找到，尝试从存储中获取用户信息
				if (!token) {
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.token) {
						token = userInfo.token;
					}
				}
			} catch (error) {
				console.warn('获取token失败:', error);
			}

			const response = await uni.request({
				url: url,
				method: 'POST',
				data: data,
				header: {
					'Content-Type': 'application/json',
					...(token ? { 'Authorization': `Bearer ${token}` } : {}),
					...options.headers
				},
				timeout: options.timeout || this.timeout
			});

			console.log(`✅ POST响应:`, response);

			if (response.statusCode === 200 && response.data.success) {
				return response.data;
			} else {
				throw new Error(response.data.message || `请求失败 (${response.statusCode})`);
			}
		} catch (error) {
			console.error(`❌ POST请求失败 (${endpoint}):`, error);
			throw new Error('请求失败: ' + error.message);
		}
	}

	/**
	 * 通用GET请求方法
	 * @param {string} endpoint - API端点（相对路径）
	 * @param {Object} options - 请求选项
	 * @returns {Promise} 请求结果
	 */
	async get(endpoint, options = {}) {
		try {
			const url = `${this.baseUrl}/api${endpoint}`;
			console.log(`📤 GET请求:`, url);

			// 获取token - 从userManager获取
			let token = null;
			try {
				// 尝试从全局获取userManager实例
				if (typeof getApp === 'function') {
					const app = getApp();
					if (app.globalData && app.globalData.userManager) {
						token = app.globalData.userManager.getToken();
					}
				}

				// 如果没有找到，尝试从存储中获取用户信息
				if (!token) {
					const userInfo = uni.getStorageSync('userInfo');
					if (userInfo && userInfo.token) {
						token = userInfo.token;
					}
				}
			} catch (error) {
				console.warn('获取token失败:', error);
			}

			const response = await uni.request({
				url: url,
				method: 'GET',
				header: {
					'Content-Type': 'application/json',
					...(token ? { 'Authorization': `Bearer ${token}` } : {}),
					...options.headers
				},
				timeout: options.timeout || this.timeout
			});

			console.log(`✅ GET响应:`, response);

			if (response.statusCode === 200 && response.data.success) {
				return response.data;
			} else {
				throw new Error(response.data.message || `请求失败 (${response.statusCode})`);
			}
		} catch (error) {
			console.error(`❌ GET请求失败 (${endpoint}):`, error);
			throw new Error('请求失败: ' + error.message);
		}
	}

}

// 创建单例实例
const apiService = new ApiService();

export default apiService;

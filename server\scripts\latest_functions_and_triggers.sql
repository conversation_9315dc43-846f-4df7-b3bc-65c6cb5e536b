-- =====================================================
-- 最新的函数和触发器创建脚本
-- 包含所有重复检查函数、清理函数和事务安全触发器
-- 创建时间: 2024-08-26
-- =====================================================

USE identify;

-- =====================================================
-- 1. 重复发送检查函数
-- =====================================================

-- 删除已存在的函数
DROP FUNCTION IF EXISTS fn_check_notification_duplicate;
DROP FUNCTION IF EXISTS fn_check_order_created_duplicate;
DROP FUNCTION IF EXISTS fn_check_order_shipped_duplicate;
DROP FUNCTION IF EXISTS fn_check_delayed_shipping_duplicate;

DELIMITER $$

-- 通用重复检查函数
CREATE FUNCTION fn_check_notification_duplicate(
    p_template_code VARCHAR(50),
    p_order_no VARCHAR(100),
    p_follower_name VARCHAR(50),
    p_extra_condition VARCHAR(500)
) RETURNS TINYINT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    -- 基础检查
    IF p_extra_condition IS NULL OR p_extra_condition = '' THEN
        SELECT COUNT(*) INTO v_count
        FROM sms_logs 
        WHERE template_code = p_template_code
        AND order_no = p_order_no
        AND follower_name = p_follower_name
        AND send_status = 1;
    ELSE
        -- 延迟通知的特殊检查（包含delayDays）
        IF p_template_code = 'SMS_493420157' AND p_extra_condition LIKE '%delayDays%' THEN
            SET @delay_days = SUBSTRING_INDEX(SUBSTRING_INDEX(p_extra_condition, '"', -2), '"', 1);
            
            SELECT COUNT(*) INTO v_count
            FROM sms_logs 
            WHERE template_code = p_template_code
            AND order_no = p_order_no
            AND follower_name = p_follower_name
            AND JSON_EXTRACT(template_params, '$.delayDays') = @delay_days
            AND send_status = 1;
        ELSE
            -- 其他情况使用基础检查
            SELECT COUNT(*) INTO v_count
            FROM sms_logs 
            WHERE template_code = p_template_code
            AND order_no = p_order_no
            AND follower_name = p_follower_name
            AND send_status = 1;
        END IF;
    END IF;
    
    RETURN IF(v_count > 0, 1, 0);
END$$

-- 订单创建通知重复检查函数
CREATE FUNCTION fn_check_order_created_duplicate(
    p_order_no VARCHAR(100),
    p_follower_name VARCHAR(50)
) RETURNS TINYINT
READS SQL DATA
DETERMINISTIC
BEGIN
    RETURN fn_check_notification_duplicate('SMS_493305115', p_order_no, p_follower_name, NULL);
END$$

-- 订单发货通知重复检查函数
CREATE FUNCTION fn_check_order_shipped_duplicate(
    p_order_no VARCHAR(100),
    p_follower_name VARCHAR(50)
) RETURNS TINYINT
READS SQL DATA
DETERMINISTIC
BEGIN
    RETURN fn_check_notification_duplicate('SMS_493260128', p_order_no, p_follower_name, NULL);
END$$

-- 延迟发货通知重复检查函数
CREATE FUNCTION fn_check_delayed_shipping_duplicate(
    p_order_no VARCHAR(100),
    p_follower_name VARCHAR(50),
    p_delay_days INT
) RETURNS TINYINT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_count INT DEFAULT 0;
    
    SELECT COUNT(*) INTO v_count
    FROM sms_logs 
    WHERE template_code = 'SMS_493420157'
    AND order_no = p_order_no
    AND follower_name = p_follower_name
    AND JSON_EXTRACT(template_params, '$.delayDays') = CAST(p_delay_days AS CHAR)
    AND send_status = 1;
    
    RETURN IF(v_count > 0, 1, 0);
END$$

-- =====================================================
-- 2. 跟单员姓名清理函数
-- =====================================================

DROP FUNCTION IF EXISTS fn_clean_follower_name$$

CREATE FUNCTION fn_clean_follower_name(
    p_follower_name VARCHAR(100)
) RETURNS VARCHAR(50)
DETERMINISTIC
BEGIN
    DECLARE v_clean_name VARCHAR(100);
    
    -- 检查输入
    IF p_follower_name IS NULL OR TRIM(p_follower_name) = '' THEN
        RETURN '跟单员';
    END IF;
    
    SET v_clean_name = p_follower_name;
    
    -- 去除手机号码（11位数字，以1开头）
    SET v_clean_name = TRIM(REGEXP_REPLACE(v_clean_name, '\\s*1[3-9][0-9]{9}\\s*', ''));
    
    -- 去除其他数字和特殊字符
    SET v_clean_name = REGEXP_REPLACE(v_clean_name, '[0-9\\-\\(\\)\\[\\]]', '');
    
    -- 去除多余的空格
    SET v_clean_name = TRIM(REGEXP_REPLACE(v_clean_name, '\\s+', ' '));
    
    -- 限制长度
    IF CHAR_LENGTH(v_clean_name) > 10 THEN
        SET v_clean_name = LEFT(v_clean_name, 10);
    END IF;
    
    -- 如果清理后为空，使用默认值
    IF v_clean_name IS NULL OR TRIM(v_clean_name) = '' THEN
        SET v_clean_name = '跟单员';
    END IF;
    
    RETURN v_clean_name;
END$$

-- =====================================================
-- 3. 模板参数验证函数
-- =====================================================

DROP FUNCTION IF EXISTS fn_validate_template_params$$

CREATE FUNCTION fn_validate_template_params(
    p_notification_type VARCHAR(50),
    p_template_params TEXT
) RETURNS BOOLEAN
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE v_is_valid BOOLEAN DEFAULT FALSE;
    
    -- 检查是否为有效JSON
    IF JSON_VALID(p_template_params) = 0 THEN
        RETURN FALSE;
    END IF;
    
    -- 根据通知类型检查必需字段
    CASE p_notification_type
        WHEN 'order_created' THEN
            SET v_is_valid = (
                JSON_EXTRACT(p_template_params, '$.follower') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.orderNo') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.customerName') IS NOT NULL
            );
            
        WHEN 'order_shipped' THEN
            SET v_is_valid = (
                JSON_EXTRACT(p_template_params, '$.follower') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.orderNo') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.customerName') IS NOT NULL
            );
            
        WHEN 'delayed_shipping' THEN
            SET v_is_valid = (
                JSON_EXTRACT(p_template_params, '$.follower') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.orderNo') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.customerName') IS NOT NULL AND
                JSON_EXTRACT(p_template_params, '$.delayDays') IS NOT NULL
            );
            
        ELSE
            SET v_is_valid = TRUE; -- 未知类型，只要是有效JSON就行
    END CASE;
    
    RETURN v_is_valid;
END$$

DELIMITER ;

-- =====================================================
-- 4. 创建触发器错误日志表
-- =====================================================

CREATE TABLE IF NOT EXISTS trigger_error_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    trigger_name VARCHAR(100) NOT NULL COMMENT '触发器名称',
    table_name VARCHAR(100) NOT NULL COMMENT '表名',
    record_id INT COMMENT '记录ID',
    error_message TEXT COMMENT '错误信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_trigger_name (trigger_name),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='触发器错误日志表';

-- =====================================================
-- 5. 事务安全的短信通知触发器
-- =====================================================

-- 删除旧的触发器
DROP TRIGGER IF EXISTS tr_shipping_detail_sms_final;
DROP TRIGGER IF EXISTS tr_img_info_sms_final;

DELIMITER $$

-- 订单创建短信通知触发器
CREATE TRIGGER tr_shipping_detail_sms_final
    AFTER INSERT ON shipping_detail
    FOR EACH ROW
BEGIN
    DECLARE v_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_allowed TINYINT DEFAULT 0;
    DECLARE v_exists INT DEFAULT 0;
    DECLARE v_clean_follower VARCHAR(50);
    DECLARE v_error_count INT DEFAULT 0;

    -- 采购员相关变量
    DECLARE v_purchaser VARCHAR(100) DEFAULT NULL;
    DECLARE v_clean_purchaser VARCHAR(50) DEFAULT NULL;
    DECLARE v_purchaser_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_purchaser_allowed TINYINT DEFAULT 0;
    DECLARE v_purchaser_exists INT DEFAULT 0;

    -- 异常处理：记录错误但不影响主流程
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
    BEGIN
        SET v_error_count = v_error_count + 1;
        INSERT IGNORE INTO trigger_error_logs (
            trigger_name, table_name, record_id, error_message, created_at
        ) VALUES (
            'tr_shipping_detail_sms_final', 'shipping_detail', NEW.id,
            CONCAT('SMS trigger error for order: ', NEW.order_no), NOW()
        );
    END;

    -- 只处理有跟单员的订单
    IF NEW.follower IS NOT NULL AND TRIM(NEW.follower) != '' THEN

        -- 清理跟单员姓名
        SET v_clean_follower = fn_clean_follower_name(NEW.follower);

        -- 1. 处理跟单员通知
        -- 获取跟单员信息（手机号和通知开关）
        SELECT phone, allowed INTO v_phone, v_allowed
        FROM follower_login
        WHERE follower_name = NEW.follower
        AND status = 1
        LIMIT 1;

        -- 检查条件：开启通知 + 有手机号
        IF v_allowed = 1 AND v_phone IS NOT NULL AND TRIM(v_phone) != '' THEN

            -- 使用统一的重复检查函数
            SET v_exists = fn_check_order_created_duplicate(NEW.order_no, NEW.follower);

            -- 如果未发送过，添加到队列
            IF v_exists = 0 THEN

                -- 插入短信队列记录
                INSERT IGNORE INTO sms_notification_queue (
                    order_no, factory_name, follower_name, phone_number,
                    notification_type, template_code, template_params, status,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_no,
                    NEW.receiver,
                    NEW.follower,
                    v_phone,
                    'order_created',
                    'SMS_493305115',
                    JSON_OBJECT(
                        'follower', v_clean_follower,
                        'orderNo', NEW.order_no,
                        'customerName', NEW.receiver,
                        'orderTime', DATE_FORMAT(NEW.created_at, '%Y/%m/%d %H:%i:%s')
                    ),
                    'pending',
                    NOW(),
                    NOW()
                );

                -- 创建延迟发货跟踪记录
                INSERT IGNORE INTO delayed_shipping_tracking (
                    order_no, factory_name, follower_name,
                    order_created_at, is_shipped,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_no,
                    NEW.receiver,
                    NEW.follower,
                    NEW.created_at,
                    0,
                    NOW(),
                    NOW()
                );

            END IF;
        END IF;

        -- 2. 处理采购员通知
        -- 查找对应的采购员
        SELECT purchaser INTO v_purchaser
        FROM caigou_order
        WHERE order_id = NEW.order_no
        LIMIT 1;

        -- 如果找到采购员
        IF v_purchaser IS NOT NULL AND TRIM(v_purchaser) != '' THEN

            -- 清理采购员姓名
            SET v_clean_purchaser = fn_clean_follower_name(v_purchaser);

            -- 检查采购员和跟单员是否为同一人（比较清理后的姓名）
            IF v_clean_purchaser != v_clean_follower THEN

                -- 获取采购员信息（通过清理后的姓名匹配）
                SELECT phone, allowed INTO v_purchaser_phone, v_purchaser_allowed
                FROM follower_login
                WHERE fn_clean_follower_name(follower_name) = v_clean_purchaser
                AND status = 1
                LIMIT 1;

                -- 检查条件：开启通知 + 有手机号
                IF v_purchaser_allowed = 1 AND v_purchaser_phone IS NOT NULL AND TRIM(v_purchaser_phone) != '' THEN

                    -- 检查是否已发送过（使用采购员姓名检查）
                    SET v_purchaser_exists = fn_check_order_created_duplicate(NEW.order_no, v_purchaser);

                    -- 如果未发送过，添加到队列
                    IF v_purchaser_exists = 0 THEN

                        -- 插入短信队列记录（通知采购员）
                        INSERT IGNORE INTO sms_notification_queue (
                            order_no, factory_name, follower_name, phone_number,
                            notification_type, template_code, template_params, status,
                            created_at, updated_at
                        ) VALUES (
                            NEW.order_no,
                            NEW.receiver,
                            v_purchaser,  -- 使用采购员姓名
                            v_purchaser_phone,
                            'order_created',
                            'SMS_493305115',
                            JSON_OBJECT(
                                'follower', v_clean_purchaser,  -- 使用采购员姓名
                                'orderNo', NEW.order_no,
                                'customerName', NEW.receiver,
                                'orderTime', DATE_FORMAT(NEW.created_at, '%Y/%m/%d %H:%i:%s')
                            ),
                            'pending',
                            NOW(),
                            NOW()
                        );

                        -- 创建延迟发货跟踪记录（采购员）
                        INSERT IGNORE INTO delayed_shipping_tracking (
                            order_no, factory_name, follower_name,
                            order_created_at, is_shipped,
                            created_at, updated_at
                        ) VALUES (
                            NEW.order_no,
                            NEW.receiver,
                            v_purchaser,  -- 使用采购员姓名
                            NEW.created_at,
                            0,
                            NOW(),
                            NOW()
                        );

                    END IF;
                END IF;
            END IF;
        END IF;
    END IF;
END$$

-- 订单发货短信通知触发器
CREATE TRIGGER tr_img_info_sms_final
    AFTER INSERT ON img_info
    FOR EACH ROW
BEGIN
    DECLARE v_follower VARCHAR(50) DEFAULT NULL;
    DECLARE v_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_allowed TINYINT DEFAULT 0;
    DECLARE v_exists INT DEFAULT 0;
    DECLARE v_clean_follower VARCHAR(50);
    DECLARE v_error_count INT DEFAULT 0;
    
    -- 异常处理：记录错误但不影响主流程
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        SET v_error_count = v_error_count + 1;
        INSERT IGNORE INTO trigger_error_logs (
            trigger_name, table_name, record_id, error_message, created_at
        ) VALUES (
            'tr_img_info_sms_final', 'img_info', NEW.id,
            CONCAT('SMS trigger error for order: ', NEW.order_number), NOW()
        );
    END;
    
    -- 获取订单的跟单员信息
    SELECT follower INTO v_follower
    FROM shipping_detail 
    WHERE order_no = NEW.order_number 
    AND receiver = NEW.factory_name
    LIMIT 1;
    
    -- 如果找到跟单员
    IF v_follower IS NOT NULL AND TRIM(v_follower) != '' THEN
        
        -- 清理跟单员姓名
        SET v_clean_follower = fn_clean_follower_name(v_follower);
        
        -- 获取跟单员信息（手机号和通知开关）
        SELECT phone, allowed INTO v_phone, v_allowed
        FROM follower_login 
        WHERE follower_name = v_follower 
        AND status = 1
        LIMIT 1;
        
        -- 检查条件：开启通知 + 有手机号
        IF v_allowed = 1 AND v_phone IS NOT NULL AND TRIM(v_phone) != '' THEN
            
            -- 使用统一的重复检查函数
            SET v_exists = fn_check_order_shipped_duplicate(NEW.order_number, v_follower);
            
            -- 如果未发送过，添加到队列
            IF v_exists = 0 THEN
                
                -- 插入短信队列记录
                INSERT IGNORE INTO sms_notification_queue (
                    order_no, factory_name, follower_name, phone_number, 
                    notification_type, template_code, template_params, status,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_number, 
                    NEW.factory_name, 
                    v_follower, 
                    v_phone,
                    'order_shipped',
                    'SMS_493260128',
                    JSON_OBJECT(
                        'follower', v_clean_follower,
                        'orderNo', NEW.order_number,
                        'customerName', NEW.factory_name
                    ),
                    'pending',
                    NOW(),
                    NOW()
                );
                
                -- 更新延迟发货跟踪状态
                UPDATE delayed_shipping_tracking
                SET is_shipped = 1,
                    shipped_at = NOW(),
                    updated_at = NOW()
                WHERE order_no = NEW.order_number 
                AND factory_name = NEW.factory_name;
                
            END IF;
        END IF;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 6. 验证创建结果
-- =====================================================

SELECT '=== 创建的函数列表 ===' as section;

SELECT 
    ROUTINE_NAME as function_name,
    ROUTINE_TYPE as type,
    CREATED as created_time
FROM information_schema.ROUTINES 
WHERE ROUTINE_SCHEMA = 'identify' 
AND ROUTINE_TYPE = 'FUNCTION'
AND ROUTINE_NAME LIKE 'fn_%'
ORDER BY ROUTINE_NAME;

SELECT '=== 创建的触发器列表 ===' as section;

SELECT 
    TRIGGER_NAME as trigger_name,
    EVENT_MANIPULATION as event_type,
    EVENT_OBJECT_TABLE as table_name,
    CREATED as created_time
FROM information_schema.TRIGGERS 
WHERE TRIGGER_SCHEMA = 'identify' 
AND TRIGGER_NAME LIKE '%sms_final%'
ORDER BY TRIGGER_NAME;

SELECT '✅ 最新函数和触发器创建完成！' as result;

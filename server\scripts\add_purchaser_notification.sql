-- =====================================================
-- 增加采购员通知功能的数据库脚本
-- 修改触发器以支持同时通知跟单员和采购员
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 删除旧的触发器
-- =====================================================

DROP TRIGGER IF EXISTS tr_shipping_detail_sms_final;

-- =====================================================
-- 2. 创建新的触发器（支持采购员通知）
-- =====================================================

DELIMITER $$

-- 订单创建短信通知触发器（支持跟单员和采购员双重通知）
CREATE TRIGGER tr_shipping_detail_sms_final
    AFTER INSERT ON shipping_detail
    FOR EACH ROW
BEGIN
    DECLARE v_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_allowed TINYINT DEFAULT 0;
    DECLARE v_exists INT DEFAULT 0;
    DECLARE v_clean_follower VARCHAR(50);
    DECLARE v_error_count INT DEFAULT 0;
    
    -- 采购员相关变量
    DECLARE v_purchaser VARCHAR(100) DEFAULT NULL;
    DECLARE v_clean_purchaser VARCHAR(50) DEFAULT NULL;
    DECLARE v_purchaser_phone VARCHAR(20) DEFAULT NULL;
    DECLARE v_purchaser_allowed TINYINT DEFAULT 0;
    DECLARE v_purchaser_exists INT DEFAULT 0;
    
    -- 异常处理：记录错误但不影响主流程
    DECLARE CONTINUE HANDLER FOR SQLEXCEPTION 
    BEGIN
        SET v_error_count = v_error_count + 1;
        INSERT IGNORE INTO trigger_error_logs (
            trigger_name, table_name, record_id, error_message, created_at
        ) VALUES (
            'tr_shipping_detail_sms_final', 'shipping_detail', NEW.id, 
            CONCAT('SMS trigger error for order: ', NEW.order_no), NOW()
        );
    END;
    
    -- 只处理有跟单员的订单
    IF NEW.follower IS NOT NULL AND TRIM(NEW.follower) != '' THEN
        
        -- 清理跟单员姓名
        SET v_clean_follower = fn_clean_follower_name(NEW.follower);
        
        -- 1. 处理跟单员通知
        -- 获取跟单员信息（手机号和通知开关）
        SELECT phone, allowed INTO v_phone, v_allowed
        FROM follower_login
        WHERE follower_name = NEW.follower
        AND status = 1
        LIMIT 1;
        
        -- 检查条件：开启通知 + 有手机号
        IF v_allowed = 1 AND v_phone IS NOT NULL AND TRIM(v_phone) != '' THEN
            
            -- 使用统一的重复检查函数
            SET v_exists = fn_check_order_created_duplicate(NEW.order_no, NEW.follower);
            
            -- 如果未发送过，添加到队列
            IF v_exists = 0 THEN
                
                -- 插入短信队列记录
                INSERT IGNORE INTO sms_notification_queue (
                    order_no, factory_name, follower_name, phone_number, 
                    notification_type, template_code, template_params, status,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_no, 
                    NEW.receiver, 
                    NEW.follower, 
                    v_phone,
                    'order_created',
                    'SMS_493305115',
                    JSON_OBJECT(
                        'follower', v_clean_follower,
                        'orderNo', NEW.order_no,
                        'customerName', NEW.receiver,
                        'orderTime', DATE_FORMAT(NEW.created_at, '%Y/%m/%d %H:%i:%s')
                    ),
                    'pending',
                    NOW(),
                    NOW()
                );
                
                -- 创建延迟发货跟踪记录
                INSERT IGNORE INTO delayed_shipping_tracking (
                    order_no, factory_name, follower_name,
                    order_created_at, is_shipped,
                    created_at, updated_at
                ) VALUES (
                    NEW.order_no,
                    NEW.receiver,
                    NEW.follower,
                    NEW.created_at,
                    0,
                    NOW(),
                    NOW()
                );
                
            END IF;
        END IF;
        
        -- 2. 处理采购员通知
        -- 查找对应的采购员
        SELECT purchaser INTO v_purchaser
        FROM caigou_order
        WHERE order_id = NEW.order_no
        LIMIT 1;
        
        -- 如果找到采购员
        IF v_purchaser IS NOT NULL AND TRIM(v_purchaser) != '' THEN
            
            -- 清理采购员姓名
            SET v_clean_purchaser = fn_clean_follower_name(v_purchaser);
            
            -- 检查采购员和跟单员是否为同一人（比较清理后的姓名）
            IF v_clean_purchaser != v_clean_follower THEN
                
                -- 获取采购员信息（通过清理后的姓名匹配）
                SELECT phone, allowed INTO v_purchaser_phone, v_purchaser_allowed
                FROM follower_login
                WHERE fn_clean_follower_name(follower_name) = v_clean_purchaser
                AND status = 1
                LIMIT 1;
                
                -- 检查条件：开启通知 + 有手机号
                IF v_purchaser_allowed = 1 AND v_purchaser_phone IS NOT NULL AND TRIM(v_purchaser_phone) != '' THEN
                    
                    -- 检查是否已发送过（使用采购员姓名检查）
                    SET v_purchaser_exists = fn_check_order_created_duplicate(NEW.order_no, v_purchaser);
                    
                    -- 如果未发送过，添加到队列
                    IF v_purchaser_exists = 0 THEN
                        
                        -- 插入短信队列记录（通知采购员）
                        INSERT IGNORE INTO sms_notification_queue (
                            order_no, factory_name, follower_name, phone_number, 
                            notification_type, template_code, template_params, status,
                            created_at, updated_at
                        ) VALUES (
                            NEW.order_no, 
                            NEW.receiver, 
                            v_purchaser,  -- 使用采购员姓名
                            v_purchaser_phone,
                            'order_created',
                            'SMS_493305115',
                            JSON_OBJECT(
                                'follower', v_clean_purchaser,  -- 使用采购员姓名
                                'orderNo', NEW.order_no,
                                'customerName', NEW.receiver,
                                'orderTime', DATE_FORMAT(NEW.created_at, '%Y/%m/%d %H:%i:%s')
                            ),
                            'pending',
                            NOW(),
                            NOW()
                        );
                        
                        -- 创建延迟发货跟踪记录（采购员）
                        INSERT IGNORE INTO delayed_shipping_tracking (
                            order_no, factory_name, follower_name,
                            order_created_at, is_shipped,
                            created_at, updated_at
                        ) VALUES (
                            NEW.order_no,
                            NEW.receiver,
                            v_purchaser,  -- 使用采购员姓名
                            NEW.created_at,
                            0,
                            NOW(),
                            NOW()
                        );
                        
                    END IF;
                END IF;
            END IF;
        END IF;
    END IF;
END$$

DELIMITER ;

-- =====================================================
-- 3. 验证触发器创建
-- =====================================================

-- 查看触发器是否创建成功
SHOW TRIGGERS LIKE 'shipping_detail';

-- =====================================================
-- 4. 测试说明
-- =====================================================

/*
测试步骤：
1. 确保 caigou_order 表中有测试数据，包含 order_id 和 purchaser 字段
2. 确保 follower_login 表中有对应的跟单员和采购员数据，包含 phone 和 allowed 字段
3. 向 shipping_detail 表插入测试数据：
   INSERT INTO shipping_detail (order_no, receiver, follower, created_at) 
   VALUES ('TEST001', '测试工厂', '测试跟单员', NOW());
4. 检查 sms_notification_queue 表是否生成了对应的通知记录
5. 检查 delayed_shipping_tracking 表是否生成了对应的跟踪记录

预期结果：
- 如果跟单员和采购员是不同的人，应该生成2条通知记录和2条跟踪记录
- 如果跟单员和采购员是同一人，应该只生成1条通知记录和1条跟踪记录
- 如果某个人未配置手机号或未开启通知，则不会为该人生成通知记录
*/

SET FOREIGN_KEY_CHECKS = 1;

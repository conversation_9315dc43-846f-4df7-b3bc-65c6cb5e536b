/**
 * 订单管理路由
 * 处理订单相关的API请求
 */

const express = require('express');
const router = express.Router();
const { query } = require('../config/database');
const { verifyToken } = require('./auth');

/**
 * 获取订单发货状态
 * @param {string} orderNo 订单号
 * @param {string} createdAt 订单创建时间
 * @returns {Object} 发货状态信息
 */
async function getOrderShippingStatus(orderNo, createdAt) {
    try {
        // 检查是否已发货（在shipping_upload表中查找）
        const shippingSql = `
            SELECT COUNT(*) as shipped_count
            FROM shipping_upload
            WHERE order_no = ?
        `;
        const shippingResult = await query(shippingSql, [orderNo]);
        const isShipped = shippingResult[0].shipped_count > 0;

        if (isShipped) {
            return {
                status: 'shipped',
                text: '已发货',
                delayDays: 0
            };
        }

        // 计算延迟天数
        const orderDate = new Date(createdAt);
        const currentDate = new Date();
        const timeDiff = currentDate.getTime() - orderDate.getTime();
        const delayDays = Math.floor(timeDiff / (1000 * 3600 * 24));

        // 判断是否超时（超过3天）
        if (delayDays > 3) {
            return {
                status: 'overdue',
                text: '超时',
                delayDays: delayDays
            };
        } else {
            return {
                status: 'pending',
                text: '未发货',
                delayDays: delayDays
            };
        }
    } catch (error) {
        console.error('获取订单发货状态失败:', error);
        return {
            status: 'unknown',
            text: '状态未知',
            delayDays: 0
        };
    }
}

/**
 * 获取订单历史列表
 * GET /api/orders/history
 */
router.get('/history', verifyToken, async (req, res) => {
    try {
        // 从token中获取用户信息
        const userId = req.user.id;
        
        // 先获取用户的工厂名称
        const userSql = `
            SELECT factory_name 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(userSql, [userId]);
        
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        
        const factoryName = users[0].factory_name;
        
        // 基于shipping_detail表查询该工厂的订单历史
        const orderSql = `
            SELECT DISTINCT
                order_no as order_number,
                receiver,
                created_at
            FROM shipping_detail
            WHERE receiver = ?
            ORDER BY created_at DESC
            LIMIT 10
        `;

        const orders = await query(orderSql, [factoryName]);

        // 为每个订单添加图片统计信息
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [factoryName, order.order_number]);
                return {
                    ...order,
                    image_count: stats[0]?.image_count || 0,
                    created_date: order.created_at,
                    latest_upload: order.created_at
                };
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0,
                    created_date: order.created_at,
                    latest_upload: order.created_at
                };
            }
        }));
        
        res.json({
            success: true,
            message: '获取订单历史成功',
            data: ordersWithStats
        });

    } catch (error) {
        console.error('❌ 获取订单历史失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 查询订单信息（基于shipping_detail表，支持模糊查询）
 * GET /api/orders/query/:orderNumber
 */
router.get('/query/:orderNumber', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const userId = req.user.id;

        if (!orderNumber || orderNumber.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '订单号不能为空'
            });
        }

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;
        const searchTerm = orderNumber.trim();

        // 基于shipping_detail表进行模糊查询
        const orderSql = `
            SELECT DISTINCT
                order_no as order_number,
                receiver,
                created_at
            FROM shipping_detail
            WHERE receiver = ? AND order_no LIKE ?
            ORDER BY
                CASE
                    WHEN order_no = ? THEN 1
                    WHEN order_no LIKE ? THEN 2
                    ELSE 3
                END,
                created_at DESC
            LIMIT 10
        `;

        // 构建查询参数
        const searchPattern = `%${searchTerm}%`;
        const startPattern = `${searchTerm}%`;
        const queryParams = [factoryName, searchPattern, searchTerm, startPattern];

        const orders = await query(orderSql, queryParams);

        if (orders.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到匹配的订单'
            });
        }

        // 为每个订单添加图片统计信息（如果需要的话）
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [factoryName, order.order_number]);
                return {
                    ...order,
                    image_count: stats[0]?.image_count || 0
                };
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0
                };
            }
        }));

        // 如果只有一个结果，返回单个订单；如果有多个结果，返回列表
        const result = ordersWithStats.length === 1 ? ordersWithStats[0] : ordersWithStats;

        res.json({
            success: true,
            message: ordersWithStats.length === 1 ? '查询订单成功' : `找到 ${ordersWithStats.length} 个匹配的订单`,
            data: result,
            count: ordersWithStats.length,
            searchTerm: searchTerm
        });

        console.log(`🔍 模糊查询订单: "${searchTerm}" (${factoryName}) - 找到 ${ordersWithStats.length} 个结果`);

    } catch (error) {
        console.error('❌ 查询订单失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的发货单号列表
 * GET /api/orders/:orderNumber/shipping-numbers
 */
router.get('/:orderNumber/shipping-numbers', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 查询该订单对应的发货单号列表
        const shippingSql = `
            SELECT DISTINCT shipping_no
            FROM shipping_detail
            WHERE receiver = ? AND order_no = ? AND shipping_no IS NOT NULL AND shipping_no != ''
            ORDER BY shipping_no
        `;

        const shippingNumbers = await query(shippingSql, [factoryName, orderNumber]);

        // 如果没有发货单号，返回默认值
        const result = shippingNumbers.length > 0
            ? shippingNumbers.map(row => row.shipping_no)
            : ['DEFAULT'];

        res.json({
            success: true,
            message: '获取发货单号列表成功',
            data: result
        });

    } catch (error) {
        console.error('❌ 获取发货单号失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的批次号列表
 * GET /api/orders/:orderNumber/batch-numbers
 */
router.get('/:orderNumber/batch-numbers', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const { shipping_number } = req.query;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 构建查询条件
        let whereCondition = 'WHERE factory_name = ? AND order_number = ?';
        let queryParams = [factoryName, orderNumber];

        if (shipping_number && shipping_number !== 'ALL') {
            whereCondition += ' AND shipping_number = ?';
            queryParams.push(shipping_number);
        }

        // 查询该订单对应的批次号列表（从图片表和发货信息表合并查询）
        const batchSql = `
            SELECT DISTINCT batch as batch_number
            FROM (
                SELECT COALESCE(batch, 1) as batch
                FROM Img_Info
                ${whereCondition}
                UNION
                SELECT COALESCE(batch, 1) as batch
                FROM shipping_upload
                WHERE receiver = ? AND order_no = ?
                ${shipping_number && shipping_number !== 'ALL' ? 'AND shipping_no = ?' : ''}
            ) combined
            ORDER BY batch_number
        `;

        // 构建合并查询的参数
        let combinedParams = [...queryParams, factoryName, orderNumber];
        if (shipping_number && shipping_number !== 'ALL') {
            combinedParams.push(shipping_number);
        }

        const batchNumbers = await query(batchSql, combinedParams);

        // 如果没有批次号，返回默认值
        const result = batchNumbers.length > 0
            ? batchNumbers.map(row => row.batch_number)
            : [1];

        res.json({
            success: true,
            message: '获取批次号列表成功',
            data: result
        });

    } catch (error) {
        console.error('❌ 获取批次号失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的发货详细信息
 * GET /api/orders/:orderNumber/shipping-details?shipping_number=xxx
 */
router.get('/:orderNumber/shipping-details', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const { shipping_number, batch_number } = req.query;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;



        // 分两步查询：
        // 1. 先查询基础发货信息（与订单号、发货单号绑定）
        // 2. 再查询录入信息（与订单号、发货单号、批次号绑定）

        // 查询基础发货详细信息
        let baseDetailSql = `
            SELECT
                shipping_no,
                notice_date,
                deliver_company,
                follower,
                product_name,
                spec,
                width,
                weight,
                quantity,
                remark,
                created_at
            FROM shipping_detail
            WHERE receiver = ? AND order_no = ?
        `;

        let baseParams = [factoryName, orderNumber];

        if (shipping_number && shipping_number !== 'ALL') {
            baseDetailSql += ' AND shipping_no = ?';
            baseParams.push(shipping_number);
        }

        baseDetailSql += ' ORDER BY notice_date DESC, shipping_no';

        // 查询录入信息（按批次号过滤）
        let uploadInfoSql = `
            SELECT
                shipping_no,
                actual_quantity,
                length,
                weight_total,
                actual_price,
                COALESCE(batch, 1) as batch
            FROM shipping_upload
            WHERE receiver = ? AND order_no = ?
        `;

        if (shipping_number && shipping_number !== 'ALL') {
            uploadInfoSql += ' AND shipping_no = ?';
        }

        if (batch_number) {
            uploadInfoSql += ' AND COALESCE(batch, 1) = ?';
        }



        let uploadParams = [factoryName, orderNumber];
        if (shipping_number && shipping_number !== 'ALL') {
            uploadParams.push(shipping_number);
        }
        if (batch_number) {
            uploadParams.push(parseInt(batch_number));
        }

        // 执行查询
        const [baseDetails, uploadInfos] = await Promise.all([
            query(baseDetailSql, baseParams),
            query(uploadInfoSql, uploadParams)
        ]);

        // 合并数据：基础信息 + 对应批次的录入信息
        const shippingDetails = baseDetails.map(baseDetail => {
            const uploadInfo = uploadInfos.find(info => info.shipping_no === baseDetail.shipping_no);
            return {
                ...baseDetail,
                actual_quantity: uploadInfo?.actual_quantity || null,
                length: uploadInfo?.length || null,
                weight_total: uploadInfo?.weight_total || null,
                actual_price: uploadInfo?.actual_price || null,
                batch: uploadInfo?.batch || (batch_number ? parseInt(batch_number) : 1)
            };
        });



        if (shippingDetails.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到该订单的发货信息'
            });
        }

        res.json({
            success: true,
            message: '获取发货详细信息成功',
            data: shippingDetails
        });

    } catch (error) {
        console.error('❌ 获取发货详细信息失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单的图片列表
 * GET /api/orders/:orderNumber/images
 */
router.get('/:orderNumber/images', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 查询订单的图片列表
        const imagesSql = `
            SELECT
                id,
                image_name,
                image_path,
                file_size,
                upload_date
            FROM Img_Info
            WHERE factory_name = ? AND order_number = ?
            ORDER BY upload_date DESC
        `;

        const images = await query(imagesSql, [factoryName, orderNumber]);

        res.json({
            success: true,
            message: '获取图片列表成功',
            data: images
        });

        console.log(`🖼️ 获取订单图片: ${orderNumber}, 共 ${images.length} 张图片`);

    } catch (error) {
        console.error('❌ 获取订单图片失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});



/**
 * 获取订单统计信息
 * GET /api/orders/stats
 */
router.get('/stats', verifyToken, async (req, res) => {
    try {
        const userId = req.user.id;
        
        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name 
            FROM Factory_Login 
            WHERE id = ? AND status = 1
        `;
        
        const users = await query(userSql, [userId]);
        
        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }
        
        const factoryName = users[0].factory_name;
        
        // 获取统计信息
        const statsSql = `
            SELECT 
                COUNT(DISTINCT order_number) as total_orders,
                COUNT(*) as total_images,
                SUM(file_size) as total_size,
                DATE(MAX(upload_date)) as last_upload_date
            FROM Img_Info 
            WHERE factory_name = ?
        `;
        
        const stats = await query(statsSql, [factoryName]);
        
        // 获取今日统计
        const todayStatsSql = `
            SELECT 
                COUNT(DISTINCT order_number) as today_orders,
                COUNT(*) as today_images
            FROM Img_Info 
            WHERE factory_name = ? AND DATE(upload_date) = CURDATE()
        `;
        
        const todayStats = await query(todayStatsSql, [factoryName]);
        
        const result = {
            ...stats[0],
            ...todayStats[0]
        };
        
        res.json({
            success: true,
            message: '获取统计信息成功',
            data: result
        });
        
        console.log(`📊 获取订单统计: ${factoryName}`);

    } catch (error) {
        console.error('❌ 获取订单统计失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 获取指定订单和发货单号的所有批次录入信息
 * GET /api/orders/:orderNumber/shipping-upload-info
 */
router.get('/:orderNumber/shipping-upload-info', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const { shipping_number } = req.query;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 构建查询条件
        let whereCondition = 'WHERE receiver = ? AND order_no = ?';
        let queryParams = [factoryName, orderNumber];

        if (shipping_number && shipping_number !== 'ALL') {
            whereCondition += ' AND shipping_no = ?';
            queryParams.push(shipping_number);
        }

        // 查询所有批次的录入信息
        const uploadInfoSql = `
            SELECT
                shipping_no,
                actual_quantity,
                length,
                weight_total,
                actual_price,
                COALESCE(batch, 1) as batch,
                uploadtime
            FROM shipping_upload
            ${whereCondition}
            ORDER BY shipping_no, batch
        `;

        const uploadInfos = await query(uploadInfoSql, queryParams);

        res.json({
            success: true,
            message: '获取录入信息成功',
            data: uploadInfos
        });

    } catch (error) {
        console.error('❌ 获取录入信息失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 新增发货信息
 * POST /api/orders/add-shipping-info
 */
router.post('/add-shipping-info', verifyToken, async (req, res) => {
    try {
        const { orderNumber, shippingNumber, batchNumber, actual_quantity, length, weight_total, actual_price } = req.body;
        const userId = req.user.id;

        // 验证必填字段（只验证订单号和发货单号）
        if (!orderNumber || !shippingNumber) {
            return res.status(400).json({
                success: false,
                message: '订单号和发货单号不能为空'
            });
        }

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;

        // 首先检查shipping_upload表结构，确保字段存在
        try {
            const checkColumnsSQL = `
                SELECT COLUMN_NAME
                FROM INFORMATION_SCHEMA.COLUMNS
                WHERE TABLE_SCHEMA = 'identify'
                AND TABLE_NAME = 'shipping_upload'
                AND COLUMN_NAME IN ('receiver', 'order_no', 'shipping_no', 'uploadtime', 'actual_quantity', 'length', 'weight_total', 'actual_price', 'batch', 'status')
            `;

            const columns = await query(checkColumnsSQL);
            const existingColumns = columns.map(col => col.COLUMN_NAME);

            // 检查是否已存在相同的发货记录（包括批次号），并检查status状态
            const checkSql = `
                SELECT id, COALESCE(status, NULL) as status FROM shipping_upload
                WHERE receiver = ? AND order_no = ? AND shipping_no = ? AND COALESCE(batch, 1) = ?
            `;

            const batchNum = batchNumber || 1;
            const existingRecords = await query(checkSql, [factoryName, orderNumber, shippingNumber, batchNum]);

            if (existingRecords.length > 0) {
                const currentStatus = existingRecords[0].status;

                // 检查status状态：如果为0则不允许录入
                if (currentStatus === 0) {
                    return res.status(403).json({
                        success: false,
                        message: '发货单已打印，如需修改请联系负责人',
                        code: 'STATUS_LOCKED'
                    });
                }
                // 如果存在，更新记录 - 只更新存在的字段
                let updateFields = [];
                let updateValues = [];

                // 添加上传时间更新
                updateFields.push('uploadtime = NOW()');

                if (existingColumns.includes('batch')) {
                    updateFields.push('batch = ?');
                    updateValues.push(batchNum);
                }
                if (existingColumns.includes('actual_quantity')) {
                    updateFields.push('actual_quantity = ?');
                    updateValues.push(actual_quantity);
                }
                if (existingColumns.includes('length')) {
                    updateFields.push('length = ?');
                    updateValues.push(length);
                }
                if (existingColumns.includes('weight_total')) {
                    updateFields.push('weight_total = ?');
                    updateValues.push(weight_total);
                }
                if (existingColumns.includes('actual_price')) {
                    updateFields.push('actual_price = ?');
                    updateValues.push(actual_price);
                }

                // 如果status字段存在且当前为null，则设置为1
                if (existingColumns.includes('status')) {
                    const currentStatus = existingRecords[0].status;
                    if (currentStatus === null) {
                        updateFields.push('status = ?');
                        updateValues.push(1);
                    }
                }

                if (updateFields.length > 0) {
                    const updateSql = `
                        UPDATE shipping_upload
                        SET ${updateFields.join(', ')}
                        WHERE receiver = ? AND order_no = ? AND shipping_no = ? AND COALESCE(batch, 1) = ?
                    `;

                    updateValues.push(factoryName, orderNumber, shippingNumber, batchNum);
                    await query(updateSql, updateValues);

                    console.log(`📝 更新shipping_upload发货信息: ${orderNumber} - ${shippingNumber}`);
                } else {
                    console.log(`⚠️ 没有可更新的字段: ${orderNumber} - ${shippingNumber}`);
                }
            } else {
                // 如果不存在，插入新记录到shipping_upload表
                let insertFields = ['receiver', 'order_no', 'shipping_no', 'uploadtime'];
                let insertPlaceholders = ['?', '?', '?', 'NOW()'];
                let insertValues = [factoryName, orderNumber, shippingNumber];

                // 如果status字段存在，则设置为1
                if (existingColumns.includes('status')) {
                    insertFields.push('status');
                    insertPlaceholders.push('?');
                    insertValues.push(1);
                }

                if (existingColumns.includes('batch')) {
                    insertFields.push('batch');
                    insertPlaceholders.push('?');
                    insertValues.push(batchNum);
                }
                if (existingColumns.includes('actual_quantity')) {
                    insertFields.push('actual_quantity');
                    insertPlaceholders.push('?');
                    insertValues.push(actual_quantity);
                }
                if (existingColumns.includes('length')) {
                    insertFields.push('length');
                    insertPlaceholders.push('?');
                    insertValues.push(length);
                }
                if (existingColumns.includes('weight_total')) {
                    insertFields.push('weight_total');
                    insertPlaceholders.push('?');
                    insertValues.push(weight_total);
                }
                if (existingColumns.includes('actual_price')) {
                    insertFields.push('actual_price');
                    insertPlaceholders.push('?');
                    insertValues.push(actual_price);
                }

                const insertSql = `
                    INSERT INTO shipping_upload (${insertFields.join(', ')})
                    VALUES (${insertPlaceholders.join(', ')})
                `;

                await query(insertSql, insertValues);

                console.log(`➕ 新增shipping_upload发货信息: ${orderNumber} - ${shippingNumber}`);
            }
        } catch (columnCheckError) {
            console.error('❌ 检查shipping_upload表结构失败:', columnCheckError);
            throw new Error('shipping_upload数据库表结构检查失败');
        }

        res.json({
            success: true,
            message: '发货信息保存成功'
        });

    } catch (error) {
        console.error('❌ 保存发货信息失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 检查录入状态
 * GET /api/orders/:orderNumber/input-status?shipping_number=xxx&batch_number=xxx
 */
router.get('/:orderNumber/input-status', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const { shipping_number, batch_number } = req.query;
        const userId = req.user.id;

        // 获取用户的工厂名称
        const userSql = `
            SELECT factory_name
            FROM Factory_Login
            WHERE id = ? AND status = 1
        `;

        const users = await query(userSql, [userId]);

        if (users.length === 0) {
            return res.status(404).json({
                success: false,
                message: '用户不存在'
            });
        }

        const factoryName = users[0].factory_name;
        const batchNum = batch_number || 1;

        // 检查录入状态
        const statusSql = `
            SELECT COALESCE(status, NULL) as status
            FROM shipping_upload
            WHERE receiver = ? AND order_no = ? AND shipping_no = ? AND COALESCE(batch, 1) = ?
        `;

        const statusResult = await query(statusSql, [factoryName, orderNumber, shipping_number, batchNum]);

        let inputStatus = {
            canInput: true,
            status: null,
            message: '可以录入信息'
        };

        if (statusResult.length > 0) {
            const currentStatus = statusResult[0].status;

            if (currentStatus === 0) {
                inputStatus = {
                    canInput: false,
                    status: 0,
                    message: '该发货信息已被锁定，不允许录入'
                };
            } else if (currentStatus === 1) {
                inputStatus = {
                    canInput: true,
                    status: 1,
                    message: '可以继续录入信息'
                };
            } else {
                // status为null，表示记录存在但status为空
                inputStatus = {
                    canInput: true,
                    status: null,
                    message: '可以录入信息'
                };
            }
        }

        res.json({
            success: true,
            message: '获取录入状态成功',
            data: inputStatus
        });

    } catch (error) {
        console.error('❌ 获取录入状态失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 跟单员获取订单历史列表
 * GET /api/orders/follower-history
 */
router.get('/follower-history', verifyToken, async (req, res) => {
    try {
        // 从token中获取跟单员信息
        const username = req.user.username;

        // 验证是否为跟单员用户
        const followerSql = `
            SELECT id, username, follower_name, position
            FROM follower_login
            WHERE username = ? AND status = 1
        `;

        const followers = await query(followerSql, [username]);

        if (followers.length === 0) {
            return res.status(404).json({
                success: false,
                message: '跟单员用户不存在'
            });
        }

        const followerName = followers[0].follower_name;
        const position = followers[0].position;

        let orders;

        // 根据用户身份查询不同的订单数据
        if (position === 2 || position === 3) {
            // 订坯人员或两种身份：查询caigou_order表中相同purchaser字段对应的订单
            // 清理followerName中的手机号，只保留姓名部分
            const cleanFollowerName = followerName.replace(/\s*1[3-9]\d{9}\s*/g, '').trim();

            const orderSql = `
                SELECT DISTINCT
                    co.order_id as order_number,
                    co.supplier as factory_name,
                    co.purchaser as follower,
                    co.order_date as notice_date,
                    co.supplier as deliver_company,
                    co.product_name,
                    co.material_spec as spec,
                    co.greige_amount as quantity,
                    co.created_at,
                    co.supplier,
                    -- 订单状态判断
                    CASE
                        WHEN su.order_no IS NOT NULL THEN '已发货'
                        WHEN sd.order_no IS NOT NULL AND DATEDIFF(NOW(), sd.created_at) > 3 THEN '超时'
                        WHEN sd.order_no IS NOT NULL THEN '通知发货'
                        ELSE '未发货'
                    END as order_status,
                    sd.created_at as shipping_detail_created_at,
                    su.uploadtime as shipped_time
                FROM caigou_order co
                LEFT JOIN shipping_detail sd ON co.order_id COLLATE utf8mb4_unicode_ci = sd.order_no
                LEFT JOIN shipping_upload su ON co.order_id COLLATE utf8mb4_unicode_ci = su.order_no
                WHERE co.purchaser = ?
                ORDER BY co.created_at DESC
                LIMIT 10
            `;
            orders = await query(orderSql, [cleanFollowerName]);
        } else {
            // 跟单员：查询shipping_detail表
            const orderSql = `
                SELECT DISTINCT
                    sd.order_no as order_number,
                    sd.receiver as factory_name,
                    sd.follower,
                    sd.notice_date,
                    sd.deliver_company,
                    sd.product_name,
                    sd.spec,
                    sd.quantity,
                    sd.created_at,
                    co.supplier
                FROM shipping_detail sd
                LEFT JOIN caigou_order co ON sd.order_no COLLATE utf8mb4_0900_ai_ci = co.order_id
                WHERE sd.follower = ?
                ORDER BY sd.created_at DESC
                LIMIT 10
            `;
            orders = await query(orderSql, [followerName]);
        }

        // 为每个订单添加图片统计信息和发货状态
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [order.factory_name, order.order_number]);

                // 根据用户身份处理订单状态
                if (position === 2 || position === 3) {
                    // 订坯人员：使用SQL计算的状态，只有超时状态才计算天数
                    let delayDays = 0;
                    if (order.order_status === '超时' && order.shipping_detail_created_at) {
                        const createdDate = new Date(order.shipping_detail_created_at);
                        const currentDate = new Date();
                        delayDays = Math.floor((currentDate - createdDate) / (1000 * 60 * 60 * 24));
                    }

                    return {
                        ...order,
                        image_count: stats[0]?.image_count || 0,
                        created_date: order.created_at,
                        latest_upload: order.created_at,
                        shipping_status: order.order_status,
                        shipping_status_text: order.order_status,
                        delay_days: delayDays
                    };
                } else {
                    // 跟单员：使用原有的发货状态检查逻辑
                    const shippingStatus = await getOrderShippingStatus(order.order_number, order.created_at);

                    return {
                        ...order,
                        image_count: stats[0]?.image_count || 0,
                        created_date: order.created_at,
                        latest_upload: order.created_at,
                        shipping_status: shippingStatus.status,
                        shipping_status_text: shippingStatus.text,
                        delay_days: shippingStatus.delayDays
                    };
                }
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0,
                    created_date: order.created_at,
                    latest_upload: order.created_at,
                    shipping_status: 'unknown',
                    shipping_status_text: '状态未知',
                    delay_days: 0
                };
            }
        }));

        res.json({
            success: true,
            message: '获取跟单员订单历史成功',
            data: ordersWithStats,
            follower_info: {
                username: username,
                follower_name: followerName
            }
        });

    } catch (error) {
        console.error('❌ 获取跟单员订单历史失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 跟单员查询订单信息（支持模糊查询）
 * GET /api/orders/follower-query/:orderNumber
 */
router.get('/follower-query/:orderNumber', verifyToken, async (req, res) => {
    try {
        const { orderNumber } = req.params;
        const username = req.user.username;

        if (!orderNumber || orderNumber.trim() === '') {
            return res.status(400).json({
                success: false,
                message: '订单号不能为空'
            });
        }

        // 验证是否为跟单员用户
        const followerSql = `
            SELECT id, username, follower_name, position
            FROM follower_login
            WHERE username = ? AND status = 1
        `;

        const followers = await query(followerSql, [username]);

        if (followers.length === 0) {
            return res.status(404).json({
                success: false,
                message: '跟单员用户不存在'
            });
        }

        const followerName = followers[0].follower_name;
        const position = followers[0].position;
        const searchTerm = orderNumber.trim();

        let orders;

        // 根据用户身份查询不同的订单数据
        if (position === 2 || position === 3) {
            // 订坯人员或两种身份：查询caigou_order表中相同purchaser字段对应的订单
            // 清理followerName中的手机号，只保留姓名部分
            const cleanFollowerName = followerName.replace(/\s*1[3-9]\d{9}\s*/g, '').trim();

            const orderSql = `
                SELECT DISTINCT
                    co.order_id as order_number,
                    co.supplier as factory_name,
                    co.purchaser as follower,
                    co.order_date as notice_date,
                    co.supplier as deliver_company,
                    co.product_name,
                    co.material_spec as spec,
                    co.greige_amount as quantity,
                    co.created_at,
                    co.supplier,
                    -- 订单状态判断
                    CASE
                        WHEN su.order_no IS NOT NULL THEN '已发货'
                        WHEN sd.order_no IS NOT NULL AND DATEDIFF(NOW(), sd.created_at) > 3 THEN '超时'
                        WHEN sd.order_no IS NOT NULL THEN '通知发货'
                        ELSE '未发货'
                    END as order_status,
                    sd.created_at as shipping_detail_created_at,
                    su.uploadtime as shipped_time
                FROM caigou_order co
                LEFT JOIN shipping_detail sd ON co.order_id COLLATE utf8mb4_unicode_ci = sd.order_no
                LEFT JOIN shipping_upload su ON co.order_id COLLATE utf8mb4_unicode_ci = su.order_no
                WHERE co.purchaser = ? AND co.order_id LIKE ?
                ORDER BY
                    CASE
                        WHEN co.order_id = ? THEN 1
                        WHEN co.order_id LIKE ? THEN 2
                        ELSE 3
                    END,
                    co.created_at DESC
                LIMIT 10
            `;

            // 构建查询参数
            const searchPattern = `%${searchTerm}%`;
            const startPattern = `${searchTerm}%`;
            const queryParams = [cleanFollowerName, searchPattern, searchTerm, startPattern];

            orders = await query(orderSql, queryParams);
        } else {
            // 跟单员：查询shipping_detail表
            const orderSql = `
                SELECT DISTINCT
                    sd.order_no as order_number,
                    sd.receiver as factory_name,
                    sd.follower,
                    sd.notice_date,
                    sd.deliver_company,
                    sd.product_name,
                    sd.spec,
                    sd.quantity,
                    sd.created_at,
                    co.supplier
                FROM shipping_detail sd
                LEFT JOIN caigou_order co ON sd.order_no COLLATE utf8mb4_0900_ai_ci = co.order_id
                WHERE sd.follower = ? AND sd.order_no LIKE ?
                ORDER BY
                    CASE
                        WHEN sd.order_no = ? THEN 1
                        WHEN sd.order_no LIKE ? THEN 2
                        ELSE 3
                    END,
                    sd.created_at DESC
                LIMIT 10
            `;

            // 构建查询参数
            const searchPattern = `%${searchTerm}%`;
            const startPattern = `${searchTerm}%`;
            const queryParams = [followerName, searchPattern, searchTerm, startPattern];

            orders = await query(orderSql, queryParams);
        }

        if (orders.length === 0) {
            return res.status(404).json({
                success: false,
                message: '未找到匹配的订单'
            });
        }

        // 为每个订单添加图片统计信息和发货状态
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [order.factory_name, order.order_number]);

                // 根据用户身份处理订单状态
                if (position === 2 || position === 3) {
                    // 订坯人员：使用SQL计算的状态，只有超时状态才计算天数
                    let delayDays = 0;
                    if (order.order_status === '超时' && order.shipping_detail_created_at) {
                        const createdDate = new Date(order.shipping_detail_created_at);
                        const currentDate = new Date();
                        delayDays = Math.floor((currentDate - createdDate) / (1000 * 60 * 60 * 24));
                    }

                    return {
                        ...order,
                        image_count: stats[0]?.image_count || 0,
                        shipping_status: order.order_status,
                        shipping_status_text: order.order_status,
                        delay_days: delayDays
                    };
                } else {
                    // 跟单员：使用原有的发货状态检查逻辑
                    const shippingStatus = await getOrderShippingStatus(order.order_number, order.created_at);

                    return {
                        ...order,
                        image_count: stats[0]?.image_count || 0,
                        shipping_status: shippingStatus.status,
                        shipping_status_text: shippingStatus.text,
                        delay_days: shippingStatus.delayDays
                    };
                }
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0,
                    shipping_status: 'unknown',
                    shipping_status_text: '状态未知',
                    delay_days: 0
                };
            }
        }));

        // 如果只有一个结果，返回单个订单；如果有多个结果，返回列表
        const result = ordersWithStats.length === 1 ? ordersWithStats[0] : ordersWithStats;

        res.json({
            success: true,
            message: ordersWithStats.length === 1 ? '查询订单成功' : `找到 ${ordersWithStats.length} 个匹配的订单`,
            data: result,
            count: ordersWithStats.length,
            searchTerm: searchTerm,
            follower_info: {
                username: username,
                follower_name: followerName
            }
        });

        console.log(`🔍 跟单员模糊查询订单: "${searchTerm}" (${followerName}) - 找到 ${ordersWithStats.length} 个结果`);

    } catch (error) {
        console.error('❌ 跟单员查询订单失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

/**
 * 跟单员高级检索订单信息
 * POST /api/orders/follower-advanced-search
 */
router.post('/follower-advanced-search', verifyToken, async (req, res) => {
    try {
        const { orderNumber, factoryName, startDate, endDate } = req.body;
        const username = req.user.username;

        // 验证是否为跟单员用户
        const followerSql = `
            SELECT id, username, follower_name, position
            FROM follower_login
            WHERE username = ? AND status = 1
        `;

        const followers = await query(followerSql, [username]);

        if (followers.length === 0) {
            return res.status(404).json({
                success: false,
                message: '跟单员用户不存在'
            });
        }

        const followerName = followers[0].follower_name;
        const position = followers[0].position;

        let whereConditions, queryParams;

        // 根据用户身份构建不同的查询条件
        if (position === 2 || position === 3) {
            // 订坯人员或两种身份：查询caigou_order表
            // 清理followerName中的手机号，只保留姓名部分
            const cleanFollowerName = followerName.replace(/\s*1[3-9]\d{9}\s*/g, '').trim();
            
            baseTable = 'caigou_order';
            whereConditions = ['co.purchaser = ?'];
            queryParams = [cleanFollowerName];

            // 添加订单号筛选
            if (orderNumber && orderNumber.trim()) {
                whereConditions.push('co.order_id LIKE ?');
                queryParams.push(`%${orderNumber.trim()}%`);
            }

            // 添加坯布商（supplier）筛选
            if (factoryName && factoryName.trim()) {
                whereConditions.push('co.supplier LIKE ?');
                queryParams.push(`%${factoryName.trim()}%`);
            }

            // 添加日期范围筛选
            if (startDate) {
                whereConditions.push('DATE(co.order_date) >= ?');
                queryParams.push(startDate);
            }

            if (endDate) {
                whereConditions.push('DATE(co.order_date) <= ?');
                queryParams.push(endDate);
            }
        } else {
            // 跟单员：查询shipping_detail表
            baseTable = 'shipping_detail';
            whereConditions = ['sd.follower = ?'];
            queryParams = [followerName];

            // 添加订单号筛选
            if (orderNumber && orderNumber.trim()) {
                whereConditions.push('sd.order_no LIKE ?');
                queryParams.push(`%${orderNumber.trim()}%`);
            }

            // 添加坯布商（工厂名称）筛选
            if (factoryName && factoryName.trim()) {
                whereConditions.push('sd.receiver LIKE ?');
                queryParams.push(`%${factoryName.trim()}%`);
            }

            // 添加日期范围筛选
            if (startDate) {
                whereConditions.push('DATE(sd.notice_date) >= ?');
                queryParams.push(startDate);
            }

            if (endDate) {
                whereConditions.push('DATE(sd.notice_date) <= ?');
                queryParams.push(endDate);
            }
        }

        // 构建完整的SQL查询
        let orderSql, orders;
        
        if (position === 2 || position === 3) {
            // 订坯人员：查询caigou_order表
            orderSql = `
                SELECT DISTINCT
                    co.order_id as order_number,
                    co.supplier as factory_name,
                    co.purchaser as follower,
                    co.order_date as notice_date,
                    co.supplier as deliver_company,
                    co.product_name,
                    co.material_spec as spec,
                    co.greige_amount as quantity,
                    co.created_at,
                    co.supplier,
                    -- 订单状态判断
                    CASE
                        WHEN su.order_no IS NOT NULL THEN '已发货'
                        WHEN sd.order_no IS NOT NULL AND DATEDIFF(NOW(), sd.created_at) > 3 THEN '超时'
                        WHEN sd.order_no IS NOT NULL THEN '通知发货'
                        ELSE '未发货'
                    END as order_status,
                    sd.created_at as shipping_detail_created_at,
                    su.uploadtime as shipped_time
                FROM caigou_order co
                LEFT JOIN shipping_detail sd ON co.order_id COLLATE utf8mb4_unicode_ci = sd.order_no
                LEFT JOIN shipping_upload su ON co.order_id COLLATE utf8mb4_unicode_ci = su.order_no
                WHERE ${whereConditions.join(' AND ')}
                ORDER BY
                    CASE
                        WHEN co.order_id = ? THEN 1
                        WHEN co.order_id LIKE ? THEN 2
                        ELSE 3
                    END,
                    co.order_date DESC,
                    co.created_at DESC
                LIMIT 50
            `;

            // 为排序添加额外参数
            if (orderNumber && orderNumber.trim()) {
                queryParams.push(orderNumber.trim());
                queryParams.push(`${orderNumber.trim()}%`);
            } else {
                queryParams.push('');
                queryParams.push('');
            }

            console.log('🔍 订坯人员高级检索SQL:', orderSql);
            console.log('🔍 查询参数:', queryParams);

            orders = await query(orderSql, queryParams);
        } else {
            // 跟单员：查询shipping_detail表
            orderSql = `
                SELECT DISTINCT
                    sd.order_no as order_number,
                    sd.receiver as factory_name,
                    sd.follower,
                    sd.notice_date,
                    sd.deliver_company,
                    sd.product_name,
                    sd.spec,
                    sd.quantity,
                    sd.created_at,
                    co.supplier
                FROM shipping_detail sd
                LEFT JOIN caigou_order co ON sd.order_no COLLATE utf8mb4_0900_ai_ci = co.order_id
                WHERE ${whereConditions.join(' AND ')}
                ORDER BY
                    CASE
                        WHEN sd.order_no = ? THEN 1
                        WHEN sd.order_no LIKE ? THEN 2
                        ELSE 3
                    END,
                    sd.notice_date DESC,
                    sd.created_at DESC
                LIMIT 50
            `;

            // 为排序添加额外参数
            if (orderNumber && orderNumber.trim()) {
                queryParams.push(orderNumber.trim());
                queryParams.push(`${orderNumber.trim()}%`);
            } else {
                queryParams.push('');
                queryParams.push('');
            }

            console.log('🔍 跟单员高级检索SQL:', orderSql);
            console.log('🔍 查询参数:', queryParams);

            orders = await query(orderSql, queryParams);
        }

        // 为每个订单添加图片统计信息和发货状态
        const ordersWithStats = await Promise.all(orders.map(async (order) => {
            try {
                const statsSql = `
                    SELECT COUNT(*) as image_count
                    FROM Img_Info
                    WHERE factory_name = ? AND order_number = ?
                `;
                const stats = await query(statsSql, [order.factory_name, order.order_number]);

                // 根据用户身份处理订单状态
                if (position === 2 || position === 3) {
                    // 订坯人员：使用SQL计算的状态，只有超时状态才计算天数
                    let delayDays = 0;
                    if (order.order_status === '超时' && order.shipping_detail_created_at) {
                        const createdDate = new Date(order.shipping_detail_created_at);
                        const currentDate = new Date();
                        delayDays = Math.floor((currentDate - createdDate) / (1000 * 60 * 60 * 24));
                    }

                    return {
                        ...order,
                        image_count: stats[0]?.image_count || 0,
                        created_date: order.created_at,
                        latest_upload: order.created_at,
                        shipping_status: order.order_status,
                        shipping_status_text: order.order_status,
                        delay_days: delayDays
                    };
                } else {
                    // 跟单员：使用原有的发货状态检查逻辑
                    const shippingStatus = await getOrderShippingStatus(order.order_number, order.created_at);

                    return {
                        ...order,
                        image_count: stats[0]?.image_count || 0,
                        created_date: order.created_at,
                        latest_upload: order.created_at,
                        shipping_status: shippingStatus.status,
                        shipping_status_text: shippingStatus.text,
                        delay_days: shippingStatus.delayDays
                    };
                }
            } catch (error) {
                console.error('获取订单图片统计失败:', error);
                return {
                    ...order,
                    image_count: 0,
                    created_date: order.created_at,
                    latest_upload: order.created_at,
                    shipping_status: 'unknown',
                    shipping_status_text: '状态未知',
                    delay_days: 0
                };
            }
        }));

        res.json({
            success: true,
            message: ordersWithStats.length > 0 ? `找到 ${ordersWithStats.length} 个匹配的订单` : '未找到匹配的订单',
            data: ordersWithStats,
            count: ordersWithStats.length,
            search_criteria: {
                orderNumber: orderNumber || '',
                factoryName: factoryName || '',
                startDate: startDate || '',
                endDate: endDate || ''
            },
            follower_info: {
                username: username,
                follower_name: followerName
            }
        });

        console.log(`🔍 跟单员高级检索完成: ${followerName} - 找到 ${ordersWithStats.length} 个结果`);

    } catch (error) {
        console.error('❌ 跟单员高级检索失败:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
});

module.exports = router;

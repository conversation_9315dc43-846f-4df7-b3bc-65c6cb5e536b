{"version": 3, "file": "followerUserManager.js", "sources": ["utils/followerUserManager.js"], "sourcesContent": ["/**\n * 跟单员用户管理器\n * 管理跟单员用户的登录状态、Token等\n */\n\n// 存储键名\nconst FOLLOWER_USER_STORAGE_KEY = 'follower_user_info';\nconst FOLLOWER_TOKEN_EXPIRY_KEY = 'follower_token_expiry';\n\nclass FollowerUserManager {\n    constructor() {\n        this.userInfo = null;\n        this.isLoggedIn = false;\n        this.init();\n    }\n\n    /**\n     * 初始化用户管理器\n     */\n    init() {\n        try {\n            // 从本地存储恢复用户信息\n            const storedUserInfo = uni.getStorageSync(FOLLOWER_USER_STORAGE_KEY);\n            const tokenExpiry = uni.getStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY);\n\n            if (storedUserInfo && tokenExpiry) {\n                const expiryTime = new Date(tokenExpiry);\n                const now = new Date();\n\n                if (expiryTime > now) {\n                    // Token未过期，恢复登录状态\n                    this.userInfo = storedUserInfo;\n                    this.isLoggedIn = true;\n                    console.log('✅ 跟单员用户登录状态已恢复:', storedUserInfo.username);\n                } else {\n                    // Token已过期，清除存储\n                    console.log('⚠️ 跟单员Token已过期，清除登录状态');\n                    this.clearStorage();\n                }\n            }\n\n            // 更新全局状态\n            this.updateGlobalState();\n        } catch (error) {\n            console.error('❌ 初始化跟单员用户管理器失败:', error);\n            this.clearStorage();\n        }\n    }\n\n    /**\n     * 跟单员用户登录\n     * @param {Object} loginData 登录返回的数据\n     */\n    login(loginData) {\n        try {\n            const { username, follower_name, position, token, expires_in = 2592000 } = loginData;\n            \n            // 计算token过期时间\n            const expiryTime = new Date(Date.now() + expires_in * 1000);\n            \n            const userInfo = {\n                username,\n                follower_name,\n                position,\n                token,\n                loginTime: new Date().toISOString()\n            };\n            \n            // 保存到本地存储\n            uni.setStorageSync(FOLLOWER_USER_STORAGE_KEY, userInfo);\n            uni.setStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY, expiryTime.toISOString());\n            \n            // 更新内存状态\n            this.userInfo = userInfo;\n            this.isLoggedIn = true;\n            \n            // 更新全局状态\n            this.updateGlobalState();\n            \n            console.log('✅ 跟单员用户登录状态保存成功:', username);\n            \n            return true;\n        } catch (error) {\n            console.error('❌ 保存跟单员用户登录状态失败:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 用户登出\n     */\n    logout() {\n        try {\n            // 清除存储\n            this.clearStorage();\n            \n            // 重置内存状态\n            this.userInfo = null;\n            this.isLoggedIn = false;\n            \n            // 更新全局状态\n            this.updateGlobalState();\n            \n            console.log('✅ 跟单员用户已登出');\n            return true;\n        } catch (error) {\n            console.error('❌ 跟单员用户登出失败:', error);\n            return false;\n        }\n    }\n\n    /**\n     * 检查登录状态\n     * @returns {boolean} 是否已登录\n     */\n    checkLoginStatus() {\n        try {\n            if (!this.isLoggedIn || !this.userInfo) {\n                return false;\n            }\n\n            // 检查Token是否过期\n            const tokenExpiry = uni.getStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY);\n            if (!tokenExpiry) {\n                this.logout();\n                return false;\n            }\n\n            const expiryTime = new Date(tokenExpiry);\n            const now = new Date();\n\n            if (expiryTime <= now) {\n                console.log('⚠️ 跟单员Token已过期');\n                this.logout();\n                return false;\n            }\n\n            return true;\n        } catch (error) {\n            console.error('❌ 检查跟单员登录状态失败:', error);\n            this.logout();\n            return false;\n        }\n    }\n\n    /**\n     * 获取用户信息\n     * @returns {Object|null} 用户信息\n     */\n    getUserInfo() {\n        if (this.checkLoginStatus()) {\n            return this.userInfo;\n        }\n        return null;\n    }\n\n    /**\n     * 获取Token\n     * @returns {string|null} Token\n     */\n    getToken() {\n        if (this.checkLoginStatus()) {\n            return this.userInfo.token;\n        }\n        return null;\n    }\n\n    /**\n     * 获取用户名\n     * @returns {string|null} 用户名\n     */\n    getUsername() {\n        if (this.checkLoginStatus()) {\n            return this.userInfo.username;\n        }\n        return null;\n    }\n\n    /**\n     * 获取跟单员姓名\n     * @returns {string|null} 跟单员姓名\n     */\n    getFollowerName() {\n        if (this.checkLoginStatus()) {\n            return this.userInfo.follower_name;\n        }\n        return null;\n    }\n\n    /**\n     * 创建认证请求配置\n     * @param {Object} config 请求配置\n     */\n    createAuthRequest(config = {}) {\n        const token = this.getToken();\n\n        return {\n            ...config,\n            header: {\n                'Content-Type': 'application/json',\n                'Authorization': token ? `Bearer ${token}` : '',\n                ...config.header\n            }\n        };\n    }\n\n    /**\n     * 清除本地存储\n     */\n    clearStorage() {\n        try {\n            uni.removeStorageSync(FOLLOWER_USER_STORAGE_KEY);\n            uni.removeStorageSync(FOLLOWER_TOKEN_EXPIRY_KEY);\n        } catch (error) {\n            console.error('❌ 清除跟单员用户存储失败:', error);\n        }\n    }\n\n    /**\n     * 更新全局状态\n     */\n    updateGlobalState() {\n        try {\n            // 如果在小程序环境中，更新全局数据\n            if (typeof getApp === 'function') {\n                const app = getApp();\n                if (app.globalData) {\n                    app.globalData.followerUserManager = this;\n                    app.globalData.isFollowerLoggedIn = this.isLoggedIn;\n                    app.globalData.followerUserInfo = this.userInfo;\n                }\n            }\n        } catch (error) {\n            console.error('❌ 更新跟单员全局状态失败:', error);\n        }\n    }\n\n    /**\n     * 强制刷新登录状态\n     */\n    refreshLoginStatus() {\n        this.init();\n    }\n\n    /**\n     * 修改密码\n     * @param {string} currentPassword 当前密码\n     * @param {string} newPassword 新密码\n     * @returns {Promise<Object>} 修改结果\n     */\n    async changePassword(currentPassword, newPassword) {\n        try {\n            if (!this.checkLoginStatus()) {\n                throw new Error('用户未登录');\n            }\n\n            const token = this.getToken();\n            if (!token) {\n                throw new Error('认证token不存在');\n            }\n\n            // 发送修改密码请求\n            const response = await new Promise((resolve, reject) => {\n                uni.request({\n                    url: 'http://localhost:3001/api/auth/follower-change-password',\n                    method: 'POST',\n                    data: {\n                        currentPassword: currentPassword.trim(),\n                        newPassword: newPassword.trim()\n                    },\n                    header: {\n                        'Content-Type': 'application/json',\n                        'Authorization': `Bearer ${token}`\n                    },\n                    timeout: 10000,\n                    success: (res) => {\n                        resolve(res.data);\n                    },\n                    fail: (err) => {\n                        console.error('修改密码请求失败:', err);\n                        reject(new Error('网络连接失败'));\n                    }\n                });\n            });\n\n            if (response.success) {\n                console.log('✅ 密码修改成功');\n                return {\n                    success: true,\n                    message: response.message || '密码修改成功'\n                };\n            } else {\n                console.error('❌ 密码修改失败:', response.message);\n                return {\n                    success: false,\n                    message: response.message || '密码修改失败'\n                };\n            }\n\n        } catch (error) {\n            console.error('❌ 修改密码异常:', error);\n            return {\n                success: false,\n                message: error.message || '修改密码失败'\n            };\n        }\n    }\n}\n\n// 创建单例实例\nconst followerUserManager = new FollowerUserManager();\n\nexport default followerUserManager;\n"], "names": ["uni"], "mappings": ";;AAMA,MAAM,4BAA4B;AAClC,MAAM,4BAA4B;AAElC,MAAM,oBAAoB;AAAA,EACtB,cAAc;AACV,SAAK,WAAW;AAChB,SAAK,aAAa;AAClB,SAAK,KAAI;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA,EAKD,OAAO;AACH,QAAI;AAEA,YAAM,iBAAiBA,cAAAA,MAAI,eAAe,yBAAyB;AACnE,YAAM,cAAcA,cAAAA,MAAI,eAAe,yBAAyB;AAEhE,UAAI,kBAAkB,aAAa;AAC/B,cAAM,aAAa,IAAI,KAAK,WAAW;AACvC,cAAM,MAAM,oBAAI;AAEhB,YAAI,aAAa,KAAK;AAElB,eAAK,WAAW;AAChB,eAAK,aAAa;AAClBA,wBAAA,MAAA,MAAA,OAAA,sCAAY,mBAAmB,eAAe,QAAQ;AAAA,QAC1E,OAAuB;AAEHA,wBAAAA,yDAAY,uBAAuB;AACnC,eAAK,aAAY;AAAA,QACpB;AAAA,MACJ;AAGD,WAAK,kBAAiB;AAAA,IACzB,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,sCAAA,oBAAoB,KAAK;AACvC,WAAK,aAAY;AAAA,IACpB;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,MAAM,WAAW;AACb,QAAI;AACA,YAAM,EAAE,UAAU,eAAe,UAAU,OAAO,aAAa,OAAS,IAAG;AAG3E,YAAM,aAAa,IAAI,KAAK,KAAK,QAAQ,aAAa,GAAI;AAE1D,YAAM,WAAW;AAAA,QACb;AAAA,QACA;AAAA,QACA;AAAA,QACA;AAAA,QACA,YAAW,oBAAI,KAAM,GAAC,YAAa;AAAA,MACnD;AAGYA,oBAAAA,MAAI,eAAe,2BAA2B,QAAQ;AACtDA,oBAAAA,MAAI,eAAe,2BAA2B,WAAW,YAAa,CAAA;AAGtE,WAAK,WAAW;AAChB,WAAK,aAAa;AAGlB,WAAK,kBAAiB;AAEtBA,6EAAY,oBAAoB,QAAQ;AAExC,aAAO;AAAA,IACV,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,sCAAA,oBAAoB,KAAK;AACvC,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,SAAS;AACL,QAAI;AAEA,WAAK,aAAY;AAGjB,WAAK,WAAW;AAChB,WAAK,aAAa;AAGlB,WAAK,kBAAiB;AAEtBA,oBAAAA,MAAA,MAAA,OAAA,uCAAY,YAAY;AACxB,aAAO;AAAA,IACV,SAAQ,OAAO;AACZA,oBAAA,MAAA,MAAA,SAAA,uCAAc,gBAAgB,KAAK;AACnC,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,mBAAmB;AACf,QAAI;AACA,UAAI,CAAC,KAAK,cAAc,CAAC,KAAK,UAAU;AACpC,eAAO;AAAA,MACV;AAGD,YAAM,cAAcA,cAAAA,MAAI,eAAe,yBAAyB;AAChE,UAAI,CAAC,aAAa;AACd,aAAK,OAAM;AACX,eAAO;AAAA,MACV;AAED,YAAM,aAAa,IAAI,KAAK,WAAW;AACvC,YAAM,MAAM,oBAAI;AAEhB,UAAI,cAAc,KAAK;AACnBA,sBAAAA,MAAA,MAAA,OAAA,uCAAY,gBAAgB;AAC5B,aAAK,OAAM;AACX,eAAO;AAAA,MACV;AAED,aAAO;AAAA,IACV,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,kBAAkB,KAAK;AACrC,WAAK,OAAM;AACX,aAAO;AAAA,IACV;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc;AACV,QAAI,KAAK,oBAAoB;AACzB,aAAO,KAAK;AAAA,IACf;AACD,WAAO;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,WAAW;AACP,QAAI,KAAK,oBAAoB;AACzB,aAAO,KAAK,SAAS;AAAA,IACxB;AACD,WAAO;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,cAAc;AACV,QAAI,KAAK,oBAAoB;AACzB,aAAO,KAAK,SAAS;AAAA,IACxB;AACD,WAAO;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB;AACd,QAAI,KAAK,oBAAoB;AACzB,aAAO,KAAK,SAAS;AAAA,IACxB;AACD,WAAO;AAAA,EACV;AAAA;AAAA;AAAA;AAAA;AAAA,EAMD,kBAAkB,SAAS,IAAI;AAC3B,UAAM,QAAQ,KAAK;AAEnB,WAAO;AAAA,MACH,GAAG;AAAA,MACH,QAAQ;AAAA,QACJ,gBAAgB;AAAA,QAChB,iBAAiB,QAAQ,UAAU,KAAK,KAAK;AAAA,QAC7C,GAAG,OAAO;AAAA,MACb;AAAA,IACb;AAAA,EACK;AAAA;AAAA;AAAA;AAAA,EAKD,eAAe;AACX,QAAI;AACAA,0BAAI,kBAAkB,yBAAyB;AAC/CA,0BAAI,kBAAkB,yBAAyB;AAAA,IAClD,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,kBAAkB,KAAK;AAAA,IACxC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,oBAAoB;AAChB,QAAI;AAEA,UAAI,OAAO,WAAW,YAAY;AAC9B,cAAM,MAAM;AACZ,YAAI,IAAI,YAAY;AAChB,cAAI,WAAW,sBAAsB;AACrC,cAAI,WAAW,qBAAqB,KAAK;AACzC,cAAI,WAAW,mBAAmB,KAAK;AAAA,QAC1C;AAAA,MACJ;AAAA,IACJ,SAAQ,OAAO;AACZA,oBAAc,MAAA,MAAA,SAAA,uCAAA,kBAAkB,KAAK;AAAA,IACxC;AAAA,EACJ;AAAA;AAAA;AAAA;AAAA,EAKD,qBAAqB;AACjB,SAAK,KAAI;AAAA,EACZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQD,MAAM,eAAe,iBAAiB,aAAa;AAC/C,QAAI;AACA,UAAI,CAAC,KAAK,oBAAoB;AAC1B,cAAM,IAAI,MAAM,OAAO;AAAA,MAC1B;AAED,YAAM,QAAQ,KAAK;AACnB,UAAI,CAAC,OAAO;AACR,cAAM,IAAI,MAAM,YAAY;AAAA,MAC/B;AAGD,YAAM,WAAW,MAAM,IAAI,QAAQ,CAAC,SAAS,WAAW;AACpDA,sBAAAA,MAAI,QAAQ;AAAA,UACR,KAAK;AAAA,UACL,QAAQ;AAAA,UACR,MAAM;AAAA,YACF,iBAAiB,gBAAgB,KAAM;AAAA,YACvC,aAAa,YAAY,KAAM;AAAA,UAClC;AAAA,UACD,QAAQ;AAAA,YACJ,gBAAgB;AAAA,YAChB,iBAAiB,UAAU,KAAK;AAAA,UACnC;AAAA,UACD,SAAS;AAAA,UACT,SAAS,CAAC,QAAQ;AACd,oBAAQ,IAAI,IAAI;AAAA,UACnB;AAAA,UACD,MAAM,CAAC,QAAQ;AACXA,0BAAc,MAAA,MAAA,SAAA,uCAAA,aAAa,GAAG;AAC9B,mBAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,UAC7B;AAAA,QACrB,CAAiB;AAAA,MACjB,CAAa;AAED,UAAI,SAAS,SAAS;AAClBA,sBAAAA,MAAY,MAAA,OAAA,uCAAA,UAAU;AACtB,eAAO;AAAA,UACH,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QACjD;AAAA,MACA,OAAmB;AACHA,sBAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,SAAS,OAAO;AAC3C,eAAO;AAAA,UACH,SAAS;AAAA,UACT,SAAS,SAAS,WAAW;AAAA,QACjD;AAAA,MACa;AAAA,IAEJ,SAAQ,OAAO;AACZA,oBAAA,MAAA,MAAA,SAAA,uCAAc,aAAa,KAAK;AAChC,aAAO;AAAA,QACH,SAAS;AAAA,QACT,SAAS,MAAM,WAAW;AAAA,MAC1C;AAAA,IACS;AAAA,EACJ;AACL;AAGK,MAAC,sBAAsB,IAAI,oBAAmB;;"}
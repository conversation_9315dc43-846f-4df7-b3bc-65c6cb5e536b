# 📱 采购员通知功能实现文档

## 🎯 功能概述

在原有的跟单员通知基础上，新增了采购员通知功能。当订单创建、延迟发货或发货时，系统会同时通知跟单员和采购员（如果他们不是同一人）。

## 🔄 业务逻辑

### 1. 通知对象确定逻辑

```
订单创建 (shipping_detail 表插入数据)
    ↓
查找跟单员 (shipping_detail.follower)
    ↓
查找采购员 (caigou_order.purchaser，通过 order_id 关联)
    ↓
比较跟单员和采购员是否为同一人
    ↓
如果是同一人：只通知一次
如果不是同一人：分别通知跟单员和采购员
```

### 2. 姓名清理和比较

为了准确比较跟单员和采购员是否为同一人，系统会：
- 使用 `fn_clean_follower_name()` 函数清理姓名
- 移除手机号码（1开头的11位数字）
- 去除多余空格
- 比较清理后的姓名

### 3. 通知条件

每个通知对象必须满足以下条件：
- ✅ 用户状态启用 (`status = 1`)
- ✅ 配置了手机号 (`phone` 不为空)
- ✅ 允许接收通知 (`allowed = 1`)
- ✅ 未重复发送过相同通知

## 🛠️ 技术实现

### 1. 数据库触发器修改

**文件**: `server/scripts/add_purchaser_notification.sql`

修改了 `tr_shipping_detail_sms_final` 触发器，增加了采购员通知逻辑：

```sql
-- 1. 处理跟单员通知（原有逻辑）
IF NEW.follower IS NOT NULL THEN
    -- 获取跟单员信息并发送通知
END IF;

-- 2. 处理采购员通知（新增逻辑）
SELECT purchaser INTO v_purchaser FROM caigou_order WHERE order_id = NEW.order_no;
IF v_purchaser IS NOT NULL AND v_clean_purchaser != v_clean_follower THEN
    -- 获取采购员信息并发送通知
END IF;
```

### 2. 延迟发货检查服务修改

**文件**: `server/utils/delayedShippingChecker.js`

修改了 `sendDelayNotification` 方法，增加了采购员通知：

```javascript
async sendDelayNotification(order, delayDays) {
    // 1. 发送给跟单员
    await this.smsQueueService.addDelayedShippingNotification(...);
    
    // 2. 发送给采购员
    await this.sendDelayNotificationToPurchaser(order, delayDays);
}
```

### 3. 短信通知服务修改

**文件**: `server/utils/smsNotificationService.js`

修改了 `sendOrderShippedNotification` 方法，拆分为：
- `sendOrderShippedToFollower()` - 发送给跟单员
- `sendOrderShippedToPurchaser()` - 发送给采购员

## 📊 数据库表关系

```
shipping_detail (订单表)
├── follower (跟单员) → follower_login.follower_name
└── order_no (订单号) → caigou_order.order_id
                           └── purchaser (采购员) → follower_login.follower_name
```

### 关键表结构

#### shipping_detail (发货详情表)
- `order_no`: 订单号
- `follower`: 跟单员姓名
- `receiver`: 工厂名称

#### caigou_order (采购订单表)
- `order_id`: 订单号
- `purchaser`: 采购员姓名

#### follower_login (用户表)
- `follower_name`: 用户姓名
- `phone`: 手机号
- `allowed`: 是否允许通知
- `status`: 用户状态

## 🔄 完整流程示例

### 订单创建通知流程

1. **订单插入**: 向 `shipping_detail` 表插入新订单
   ```sql
   INSERT INTO shipping_detail (order_no, follower, receiver) 
   VALUES ('ORD001', '张三 13800138000', '测试工厂');
   ```

2. **触发器执行**:
   - 清理跟单员姓名: `张三 13800138000` → `张三`
   - 查找采购员: 从 `caigou_order` 表查找 `order_id = 'ORD001'` 的 `purchaser`
   - 假设找到采购员: `李四`

3. **比较判断**:
   - 跟单员: `张三`
   - 采购员: `李四`
   - 结果: 不是同一人，需要分别通知

4. **生成通知**:
   - 为张三生成通知记录
   - 为李四生成通知记录
   - 插入到 `sms_notification_queue` 表

5. **队列处理**: 短信队列服务异步发送通知

### 延迟发货通知流程

1. **定时检查**: 每小时检查 `delayed_shipping_tracking` 表
2. **发现延迟**: 订单超过3/5/7天未发货
3. **查找采购员**: 根据订单号查找对应采购员
4. **分别通知**: 同时通知跟单员和采购员

### 发货通知流程

1. **手动触发**: 调用 `sendOrderShippedNotification` 方法
2. **跟单员通知**: 发送给原跟单员
3. **采购员通知**: 查找并发送给采购员
4. **更新状态**: 标记订单为已发货

## 🧪 测试验证

### 1. 运行测试脚本

```bash
cd server
node test/test-purchaser-notification.js
```

### 2. 手动测试步骤

1. **准备测试数据**:
   ```sql
   -- 插入采购订单
   INSERT INTO caigou_order (order_id, purchaser) VALUES ('TEST001', '测试采购员');
   
   -- 确保用户配置
   UPDATE follower_login SET phone='13800138001', allowed=1 WHERE follower_name='测试跟单员';
   UPDATE follower_login SET phone='13800138002', allowed=1 WHERE follower_name='测试采购员';
   ```

2. **触发订单创建**:
   ```sql
   INSERT INTO shipping_detail (order_no, follower, receiver) 
   VALUES ('TEST001', '测试跟单员', '测试工厂');
   ```

3. **检查结果**:
   ```sql
   -- 检查通知队列
   SELECT * FROM sms_notification_queue WHERE order_no = 'TEST001';
   
   -- 检查跟踪记录
   SELECT * FROM delayed_shipping_tracking WHERE order_no = 'TEST001';
   ```

### 3. 预期结果

- 如果跟单员和采购员不是同一人：生成2条通知记录
- 如果跟单员和采购员是同一人：生成1条通知记录
- 如果某人未配置手机号或未开启通知：不为该人生成通知

## 🚀 部署步骤

1. **执行数据库脚本**:
   ```bash
   mysql -u username -p identify < server/scripts/add_purchaser_notification.sql
   ```

2. **重启服务**:
   ```bash
   cd server
   npm restart
   ```

3. **验证功能**:
   ```bash
   node test/test-purchaser-notification.js
   ```

## ⚠️ 注意事项

1. **数据一致性**: 确保 `caigou_order` 表中的 `order_id` 与 `shipping_detail` 表中的 `order_no` 一致
2. **用户配置**: 确保采购员在 `follower_login` 表中有对应记录
3. **姓名格式**: 系统会自动清理姓名中的手机号，但建议保持姓名格式一致
4. **重复通知**: 系统有防重复机制，相同通知不会重复发送

## 🔧 故障排查

### 1. 采购员未收到通知

检查项：
- `caigou_order` 表中是否有对应的 `order_id` 和 `purchaser`
- `follower_login` 表中采购员的 `phone` 和 `allowed` 字段
- 采购员和跟单员是否为同一人（清理后的姓名）

### 2. 重复通知

检查项：
- `sms_logs` 表中是否已有发送记录
- 重复检查函数是否正常工作

### 3. 触发器错误

检查项：
- `trigger_error_logs` 表中的错误记录
- 数据库函数 `fn_clean_follower_name` 是否存在

## 📈 性能影响

- **数据库查询**: 每次订单创建增加1次 `caigou_order` 表查询
- **通知数量**: 最多增加1倍的通知数量（如果跟单员和采购员都不同）
- **存储空间**: `sms_notification_queue` 和 `delayed_shipping_tracking` 表记录可能增加

总体性能影响较小，在可接受范围内。

/**
 * 测试采购员通知功能
 * 验证新的采购员通知系统功能
 */

const SMSNotificationService = require('../utils/smsNotificationService');
const SMSQueueService = require('../utils/smsQueueService');
const DelayedShippingChecker = require('../utils/delayedShippingChecker');
const { query } = require('../config/database');

async function testPurchaserNotificationSystem() {
    console.log('🧪 测试采购员通知系统');
    console.log('='.repeat(60));

    try {
        // 创建服务实例
        const smsNotificationService = new SMSNotificationService();
        const smsQueueService = new SMSQueueService();
        const delayedShippingChecker = new DelayedShippingChecker();

        // 测试1：检查数据库表结构
        console.log('\n📋 测试1：检查数据库表结构');
        console.log('-'.repeat(40));
        
        await checkDatabaseStructure();

        // 测试2：测试订单生成通知（跟单员）
        console.log('\n📋 测试2：测试订单生成通知（跟单员）');
        console.log('-'.repeat(40));
        
        const result1 = await smsNotificationService.sendOrderCreatedNotification(
            'TEST_PURCHASER_001',
            '测试坯布厂',
            '测试跟单员'
        );
        
        console.log('✅ 跟单员订单生成通知结果:', result1);

        // 等待1秒
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 测试3：测试延迟发货通知（包含采购员）
        console.log('\n📋 测试3：测试延迟发货通知（包含采购员）');
        console.log('-'.repeat(40));
        
        // 模拟延迟发货检查
        await testDelayedShippingWithPurchaser();

        // 测试4：测试订单发货通知（包含采购员）
        console.log('\n📋 测试4：测试订单发货通知（包含采购员）');
        console.log('-'.repeat(40));
        
        const result3 = await smsNotificationService.sendOrderShippedNotification(
            'TEST_PURCHASER_002',
            '测试坯布厂',
            '测试跟单员'
        );
        
        console.log('✅ 订单发货通知结果:', result3);

        // 测试5：检查队列中的通知记录
        console.log('\n📋 测试5：检查队列中的通知记录');
        console.log('-'.repeat(40));
        
        await checkNotificationQueue();

        console.log('\n🎉 采购员通知系统测试完成！');
        
        console.log('\n💡 提示:');
        console.log('1. 确保 caigou_order 表中有对应的测试数据');
        console.log('2. 确保 follower_login 表中配置了跟单员和采购员的手机号');
        console.log('3. 确保采购员和跟单员的 allowed 字段设置为 1');
        console.log('4. 检查 sms_notification_queue 表中是否有对应的通知记录');

    } catch (error) {
        console.error('\n❌ 采购员通知系统测试失败:', error.message);
        console.error('详细错误:', error);
    }
}

/**
 * 检查数据库表结构
 */
async function checkDatabaseStructure() {
    try {
        // 检查 caigou_order 表
        const caigouOrderSql = `
            SELECT COUNT(*) as count 
            FROM information_schema.tables 
            WHERE table_schema = 'identify' AND table_name = 'caigou_order'
        `;
        const caigouOrderResult = await query(caigouOrderSql);
        console.log(`📊 caigou_order 表存在: ${caigouOrderResult[0].count > 0 ? '✅' : '❌'}`);

        // 检查 caigou_order 表中的测试数据
        const testDataSql = `
            SELECT order_id, purchaser 
            FROM caigou_order 
            WHERE order_id LIKE 'TEST_PURCHASER_%' 
            LIMIT 5
        `;
        const testDataResult = await query(testDataSql);
        console.log(`📊 测试数据数量: ${testDataResult.length}`);
        
        if (testDataResult.length > 0) {
            console.log('📊 测试数据示例:');
            testDataResult.forEach(row => {
                console.log(`   - 订单号: ${row.order_id}, 采购员: ${row.purchaser}`);
            });
        }

        // 检查 follower_login 表中的测试用户
        const followerSql = `
            SELECT follower_name, phone, allowed, status 
            FROM follower_login 
            WHERE follower_name IN ('测试跟单员', '测试采购员') 
            OR follower_name LIKE '%测试%'
        `;
        const followerResult = await query(followerSql);
        console.log(`📊 测试用户数量: ${followerResult.length}`);
        
        if (followerResult.length > 0) {
            console.log('📊 测试用户信息:');
            followerResult.forEach(row => {
                console.log(`   - 姓名: ${row.follower_name}, 手机: ${row.phone}, 允许通知: ${row.allowed}, 状态: ${row.status}`);
            });
        }

    } catch (error) {
        console.error('❌ 检查数据库表结构失败:', error);
    }
}

/**
 * 测试延迟发货通知（包含采购员）
 */
async function testDelayedShippingWithPurchaser() {
    try {
        // 创建测试订单的延迟发货跟踪记录
        const insertTrackingSql = `
            INSERT IGNORE INTO delayed_shipping_tracking (
                order_no, factory_name, follower_name, order_created_at, is_shipped, created_at, updated_at
            ) VALUES 
            ('TEST_PURCHASER_DELAY_001', '测试坯布厂', '测试跟单员', DATE_SUB(NOW(), INTERVAL 4 DAY), 0, NOW(), NOW()),
            ('TEST_PURCHASER_DELAY_002', '测试坯布厂', '测试采购员', DATE_SUB(NOW(), INTERVAL 6 DAY), 0, NOW(), NOW())
        `;
        
        await query(insertTrackingSql);
        console.log('✅ 已创建测试延迟发货跟踪记录');

        // 手动触发延迟发货检查
        const delayedShippingChecker = new DelayedShippingChecker();
        await delayedShippingChecker.checkDelayedOrders();
        
        console.log('✅ 延迟发货检查完成');

    } catch (error) {
        console.error('❌ 测试延迟发货通知失败:', error);
    }
}

/**
 * 检查通知队列
 */
async function checkNotificationQueue() {
    try {
        const queueSql = `
            SELECT 
                order_no, 
                follower_name, 
                notification_type, 
                template_params,
                status,
                created_at
            FROM sms_notification_queue 
            WHERE order_no LIKE 'TEST_PURCHASER_%' 
            ORDER BY created_at DESC 
            LIMIT 10
        `;
        
        const queueResult = await query(queueSql);
        console.log(`📊 队列中的通知记录数量: ${queueResult.length}`);
        
        if (queueResult.length > 0) {
            console.log('📊 通知记录详情:');
            queueResult.forEach((row, index) => {
                const params = typeof row.template_params === 'string' 
                    ? JSON.parse(row.template_params) 
                    : row.template_params;
                console.log(`   ${index + 1}. 订单: ${row.order_no}, 通知人: ${row.follower_name}, 类型: ${row.notification_type}, 状态: ${row.status}`);
                console.log(`      参数: follower=${params.follower}, customerName=${params.customerName}`);
            });
        }

        // 检查延迟发货跟踪记录
        const trackingSql = `
            SELECT 
                order_no, 
                factory_name, 
                follower_name, 
                is_shipped,
                delay_days,
                created_at
            FROM delayed_shipping_tracking 
            WHERE order_no LIKE 'TEST_PURCHASER_%' 
            ORDER BY created_at DESC 
            LIMIT 10
        `;
        
        const trackingResult = await query(trackingSql);
        console.log(`📊 延迟发货跟踪记录数量: ${trackingResult.length}`);
        
        if (trackingResult.length > 0) {
            console.log('📊 跟踪记录详情:');
            trackingResult.forEach((row, index) => {
                console.log(`   ${index + 1}. 订单: ${row.order_no}, 跟踪人: ${row.follower_name}, 已发货: ${row.is_shipped ? '是' : '否'}, 延迟天数: ${row.delay_days}`);
            });
        }

    } catch (error) {
        console.error('❌ 检查通知队列失败:', error);
    }
}

// 运行测试
if (require.main === module) {
    testPurchaserNotificationSystem().catch(error => {
        console.error('❌ 程序异常:', error);
        process.exit(1);
    });
}

module.exports = testPurchaserNotificationSystem;

// 测试订单状态查询的SQL语句
const mysql = require('mysql2/promise');

// 数据库配置
const dbConfig = {
    host: '************',
    port: 3306,
    user: 'mls01',
    password: '12345@Mls',
    database: 'identify',
    charset: 'utf8mb4',
    timezone: '+08:00'
};

async function testOrderStatusQuery() {
    let connection;
    try {
        console.log('🔗 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        console.log('✅ 数据库连接成功');

        // 测试订坯人员的订单状态查询
        const testPurchaser = '高密凯蒙'; // 使用一个测试的采购员名称
        
        const orderSql = `
            SELECT DISTINCT
                co.order_id as order_number,
                co.supplier as factory_name,
                co.purchaser as follower,
                co.order_date as notice_date,
                co.supplier as deliver_company,
                co.product_name,
                co.material_spec as spec,
                co.greige_amount as quantity,
                co.created_at,
                co.supplier,
                -- 订单状态判断
                CASE 
                    WHEN su.order_no IS NOT NULL THEN '已发货'
                    WHEN sd.order_no IS NOT NULL AND DATEDIFF(NOW(), sd.created_at) > 3 THEN '超时'
                    WHEN sd.order_no IS NOT NULL THEN '通知发货'
                    ELSE '未发货'
                END as order_status,
                sd.created_at as shipping_detail_created_at,
                su.uploadtime as shipped_time
            FROM caigou_order co
            LEFT JOIN shipping_detail sd ON co.order_id = sd.order_no
            LEFT JOIN shipping_upload su ON co.order_id = su.order_no
            WHERE co.purchaser = ?
            ORDER BY co.created_at DESC
            LIMIT 10
        `;

        console.log('🔍 执行查询...');
        console.log('SQL:', orderSql);
        console.log('参数:', [testPurchaser]);

        const [rows] = await connection.execute(orderSql, [testPurchaser]);
        
        console.log('✅ 查询成功，返回', rows.length, '条记录');
        
        if (rows.length > 0) {
            console.log('\n📋 查询结果示例:');
            rows.forEach((row, index) => {
                console.log(`\n订单 ${index + 1}:`);
                console.log(`  订单号: ${row.order_number}`);
                console.log(`  工厂: ${row.factory_name}`);
                console.log(`  采购员: ${row.follower}`);
                console.log(`  订单状态: ${row.order_status}`);
                console.log(`  创建时间: ${row.created_at}`);
                if (row.shipping_detail_created_at) {
                    console.log(`  发货通知时间: ${row.shipping_detail_created_at}`);
                }
                if (row.shipped_time) {
                    console.log(`  实际发货时间: ${row.shipped_time}`);
                }
            });
        } else {
            console.log('❌ 没有找到该采购员的订单记录');
            
            // 查看数据库中有哪些采购员
            const [purchasers] = await connection.execute('SELECT DISTINCT purchaser FROM caigou_order LIMIT 10');
            console.log('\n📋 数据库中的采购员列表:');
            purchasers.forEach(p => console.log(`  - ${p.purchaser}`));
        }

    } catch (error) {
        console.error('❌ 测试失败:', error.message);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔗 数据库连接已关闭');
        }
    }
}

// 运行测试
testOrderStatusQuery();
